import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface DataItem {
  formCategory: number;
  formId: number;
  value: number | string;
  actualTitle: string;
  title: string;
  approverComments: string;
  dateOfApproval: string;
  dcfId: number;
  conversionValue: number | null;
  entity: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
  dataType: number;
  formType: number;
  uniqueId: string;
  locationId: number;
  level: number;
  reporter: string;
  reportedDate: string;
  reporting_period: string;
  rp: string[];
  reviewedDate: string;
  reporterComments: string;
  reviewer: string;
  efValue: number | null;
  submitId: number;
  reviewerComments: string;
  approver: string;
  status: string;
  indicatorTitle: string;
  indicatorId: number;
  indicatorUnit: string;
  indicatorType: number;
  emissionFactorName: string;
  emissionFactorValue: number;
  emissionFactorCo2Value: number;
  emissionFactorCh4Value: number;
  emissionFactorN2oValue: number;
  efkey: string;
  methodology: string;
  computedValue: string | number | null | undefined;
  computedCo2Value: string | number | null | undefined;
  computedCh4Value: string | number | null | undefined;
  computedN2oValue: string | number | null | undefined;
}

interface WaterWithdrawalChartProps {
  data?: DataItem[];
  year?: number; // Max year (FY26)
  fymonth?: number; // Fiscal year start month (4 = April)
  loading?: boolean; // Loading state
}

const WaterWithdrawalChart = ({
  data = [],
  year = 2026,
  fymonth = 4,
  loading = false
}: WaterWithdrawalChartProps) => {

  // Calculate fiscal years
  const currentFY = year; // FY26
  const previousFY = year - 1; // FY25

  // Generate dynamic chart title
  const dynamicTitle = `Water Withdrawal by Source: FY ${previousFY.toString().slice(-2)} vs FY ${currentFY.toString().slice(-2)}`;

  // Helper function to safely convert value to number
  const safeParseValue = (item: DataItem): number => {
    // First try computedValue
    let value = item.computedValue;
    if (value !== null && value !== undefined && value !== '' && value !== '-') {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (!isNaN(numValue)) {
        return numValue;
      }
    }

    // If computedValue is not available, use the value field
    if (item.value !== null && item.value !== undefined) {
      return typeof item.value === 'number' ? item.value : parseFloat(item.value.toString());
    }

    return 0;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Generate fiscal year months based on fymonth parameter
  const generateFiscalYearMonths = (startMonth: number): string[] => {
    const months = [
      "Apr", "May", "Jun", "Jul", "Aug", "Sep",
      "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
    ];

    // Reorder based on fiscal year start month
    const startIndex = startMonth - 1; // Convert to 0-based index
    return [...months.slice(startIndex), ...months.slice(0, startIndex)];
  };

  const fiscalMonths = generateFiscalYearMonths(fymonth);

  // Filter data by fiscal year
  const filterDataByFY = (targetFY: number) => {
    return data.filter(item => {
      if (!item.rp || item.rp.length === 0) return false;

      return item.rp.some(monthStr => {
        const fy = getFiscalYear(monthStr, fymonth);
        return fy === targetFY;
      });
    });
  };

  // Get data for each fiscal year
  const previousFYData = filterDataByFY(previousFY);
  const currentFYData = filterDataByFY(currentFY);

  // Process data by month for each fiscal year
  const processDataByMonth = (fyData: DataItem[], targetFY: number) => {
    const waterWithdrawalByMonth: number[] = [];

    // Generate month keys for the target fiscal year
    const generateMonthKeys = (fy: number, startMonth: number): string[] => {
      const keys = [];
      for (let i = 0; i < 12; i++) {
        const month = ((startMonth - 1 + i) % 12) + 1;
        const year = month >= startMonth ? fy - 1 : fy;
        keys.push(`${month.toString().padStart(2, '0')}-${year}`);
      }
      return keys;
    };

    const monthKeys = generateMonthKeys(targetFY, fymonth);

    monthKeys.forEach(monthKey => {
      let monthTotal = 0;

      fyData.forEach(item => {
        if (item.rp && item.rp.includes(monthKey)) {
          const value = safeParseValue(item);
          monthTotal += value;
        }
      });

      waterWithdrawalByMonth.push(monthTotal);
    });

    return waterWithdrawalByMonth;
  };

  // Handle loading state
  if (loading) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-12 h-12">
                <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
                <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
                <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
              </div>
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle no data state
  if (!data || data.length === 0) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <p className="text-gray-500 text-lg font-medium">No data to show</p>
              <p className="text-gray-400 text-sm mt-2">No water withdrawal data available for the selected period</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Process data for both fiscal years
  const previousFYProcessedData = processDataByMonth(previousFYData, previousFY);
  const currentFYProcessedData = processDataByMonth(currentFYData, currentFY);



  const options: Highcharts.Options = {
    chart: {
      type: 'line',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: fiscalMonths,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Water Withdrawal (Million Liters)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      shared: true,
      formatter: function() {
        let tooltip = `<b>${this.x}</b><br/>`;
        this.points?.forEach(point => {
          tooltip += `<span style="color:${point.color}">${point.series.name}</span>: <b>${point.y?.toFixed(2)} ML</b><br/>`;
        });
        return tooltip;
      }
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: false
        },
        enableMouseTracking: true,
        marker: {
          radius: 4
        }
      }
    },
    series: [
      {
        name: `FY ${previousFY.toString().slice(-2)} (Previous Year)`,
        data: previousFYProcessedData,
        color: '#75c3b3', // Using your specified color
        type: 'line',
        dashStyle: 'Solid'
      },
      {
        name: `FY ${currentFY.toString().slice(-2)} (Current Year)`,
        data: currentFYProcessedData,
        color: '#ec5624', // Using your specified color
        type: 'line',
        dashStyle: 'Dash'
      }
    ],
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default WaterWithdrawalChart;