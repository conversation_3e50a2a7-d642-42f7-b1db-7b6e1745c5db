import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';
import Highcharts from 'highcharts';

// Try to import exporting modules - with error handling
let exportingEnabled = false;
try {
  const ExportingModule = require('highcharts/modules/exporting');
  const OfflineExportingModule = require('highcharts/modules/offline-exporting');

  if (typeof ExportingModule === 'function') {
    ExportingModule(Highcharts);
  }
  if (typeof OfflineExportingModule === 'function') {
    OfflineExportingModule(Highcharts);
  }
  exportingEnabled = true;
  console.log('Highcharts exporting modules loaded successfully');
} catch (error) {
  console.warn('Highcharts exporting modules not available:', error);
  exportingEnabled = false;
}

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({
  chartRef,
  filename = 'chart',
  className = ''
}) => {

  const handleDownload = async (format: 'png' | 'jpeg') => {
    console.log('Download clicked:', format);

    if (!chartRef.current?.chart) {
      console.error('Chart reference not found');
      alert('Chart not ready for export. Please try again.');
      return;
    }

    const chart = chartRef.current.chart;
    console.log('Chart object found:', !!chart);
    console.log('Exporting enabled:', exportingEnabled);

    try {
      // Try Highcharts built-in export functionality first
      if (exportingEnabled && chart.exportChart) {
        chart.exportChart({
          type: format === 'png' ? 'image/png' : 'image/jpeg',
          filename: filename,
          scale: 2,
          sourceWidth: chart.chartWidth,
          sourceHeight: chart.chartHeight,
        });
        console.log('Highcharts export initiated successfully');
        return;
      }

      // Fallback to manual export
      console.log('Using manual export method');
      await manualExport(chart, format, filename);

    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const manualExport = async (chart: any, format: 'png' | 'jpeg', filename: string) => {
    // Find SVG element
    let svgElement: SVGElement | null = null;

    if (chart.container) {
      svgElement = chart.container.querySelector('svg');
    }

    if (!svgElement) {
      throw new Error('SVG element not found');
    }

    console.log('SVG element found for manual export');

    // Get SVG dimensions
    const svgRect = svgElement.getBoundingClientRect();
    const width = svgRect.width || 800;
    const height = svgRect.height || 400;

    // Create canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    // Set canvas size (2x for high resolution)
    const scale = 2;
    canvas.width = width * scale;
    canvas.height = height * scale;
    ctx.scale(scale, scale);

    // Set white background for JPEG
    if (format === 'jpeg') {
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, width, height);
    }

    // Clone and serialize SVG
    const svgClone = svgElement.cloneNode(true) as SVGElement;
    svgClone.setAttribute('width', width.toString());
    svgClone.setAttribute('height', height.toString());

    const svgData = new XMLSerializer().serializeToString(svgClone);

    // Create image and draw to canvas
    const img = new Image();

    return new Promise<void>((resolve, reject) => {
      img.onload = () => {
        try {
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `${filename}.${format}`;
              a.style.display = 'none';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
              console.log('Manual export successful');
              resolve();
            } else {
              reject(new Error('Failed to create blob'));
            }
          }, `image/${format}`, format === 'jpeg' ? 0.9 : 1.0);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load SVG'));

      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
