import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ 
  chartRef, 
  filename = 'chart',
  className = ''
}) => {
  const handleDownload = async (format: 'png' | 'jpeg') => {
    console.log('Download clicked:', format);

    if (!chartRef.current?.chart) {
      console.error('Chart reference not found');
      alert('Chart not ready for export. Please try again.');
      return;
    }

    const chart = chartRef.current.chart;
    console.log('Chart object found:', !!chart);

    try {
      // Get SVG from chart
      const svg = chart.getSVG({
        sourceWidth: chart.chartWidth || 800,
        sourceHeight: chart.chartHeight || 400,
        scale: 2
      });

      console.log('SVG generated, length:', svg.length);

      // Create canvas for conversion
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Set canvas size with fallbacks
      const width = chart.chartWidth || 800;
      const height = chart.chartHeight || 400;
      canvas.width = width * 2;
      canvas.height = height * 2;

      // Create image from SVG
      const img = new Image();

      const downloadPromise = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            // Set white background for JPEG
            if (format === 'jpeg') {
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, canvas.width, canvas.height);
            }

            // Draw the chart
            ctx.drawImage(img, 0, 0);

            // Convert to blob and download
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${filename}.${format}`;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                console.log('Download successful');
                resolve();
              } else {
                reject(new Error('Failed to create blob'));
              }
            }, `image/${format}`, format === 'jpeg' ? 0.9 : 1.0);
          } catch (error) {
            reject(error);
          }
        };

        img.onerror = () => {
          reject(new Error('Failed to load SVG image'));
        };
      });

      // Convert SVG to data URL
      const svgBlob = new Blob([svg], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;

      await downloadPromise;
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
