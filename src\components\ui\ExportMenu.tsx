import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ 
  chartRef, 
  filename = 'chart',
  className = ''
}) => {
  const handleDownload = async (format: 'png' | 'jpeg') => {
    console.log('Download clicked:', format);

    if (!chartRef.current) {
      console.error('Chart reference not found');
      alert('Chart not ready for export. Please try again.');
      return;
    }

    try {
      // Find the chart container element
      const chartContainer = chartRef.current.container || chartRef.current.chart?.container;

      if (!chartContainer) {
        throw new Error('Chart container not found');
      }

      console.log('Chart container found:', !!chartContainer);

      // Use html2canvas library alternative - create canvas from DOM element
      const rect = chartContainer.getBoundingClientRect();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Set canvas size (2x for high resolution)
      const scale = 2;
      canvas.width = rect.width * scale;
      canvas.height = rect.height * scale;

      // Scale the context for high resolution
      ctx.scale(scale, scale);

      // Set white background for JPEG
      if (format === 'jpeg') {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, rect.width, rect.height);
      }

      // Try to get SVG from the chart container
      const svgElement = chartContainer.querySelector('svg');

      if (svgElement) {
        console.log('SVG element found, converting to canvas');

        // Clone the SVG to avoid modifying the original
        const svgClone = svgElement.cloneNode(true) as SVGElement;

        // Create a data URL from the SVG
        const svgData = new XMLSerializer().serializeToString(svgClone);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);

        // Create image and draw to canvas
        const img = new Image();

        const downloadPromise = new Promise<void>((resolve, reject) => {
          img.onload = () => {
            try {
              ctx.drawImage(img, 0, 0, rect.width, rect.height);

              // Convert to blob and download
              canvas.toBlob((blob) => {
                if (blob) {
                  const downloadUrl = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = downloadUrl;
                  a.download = `${filename}.${format}`;
                  a.style.display = 'none';
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(downloadUrl);
                  console.log('Download successful');
                  resolve();
                } else {
                  reject(new Error('Failed to create blob'));
                }
              }, `image/${format}`, format === 'jpeg' ? 0.9 : 1.0);
            } catch (error) {
              reject(error);
            }
          };

          img.onerror = () => {
            reject(new Error('Failed to load SVG image'));
          };
        });

        img.src = url;
        await downloadPromise;
        URL.revokeObjectURL(url);

      } else {
        throw new Error('SVG element not found in chart container');
      }

    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
