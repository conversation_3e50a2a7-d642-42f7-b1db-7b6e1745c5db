import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ 
  chartRef, 
  filename = 'chart',
  className = ''
}) => {
  const handleDownload = async (format: 'png' | 'jpeg') => {
    console.log('Download clicked:', format);

    if (!chartRef.current) {
      console.error('Chart reference not found');
      alert('Chart not ready for export. Please try again.');
      return;
    }

    try {
      // Simple approach: find SVG element directly
      let svgElement: SVGElement | null = null;

      // Try to find SVG in various ways
      const refElement = chartRef.current as any;

      // Method 1: Look for SVG in the chart container
      if (refElement.chart?.container) {
        svgElement = refElement.chart.container.querySelector('svg');
        console.log('Method 1 - SVG found in chart.container:', !!svgElement);
      }

      // Method 2: Look for SVG in the ref container
      if (!svgElement && refElement.container) {
        svgElement = refElement.container.querySelector('svg');
        console.log('Method 2 - SVG found in ref.container:', !!svgElement);
      }

      // Method 3: Look for SVG in the DOM element itself
      if (!svgElement && refElement.querySelector) {
        svgElement = refElement.querySelector('svg');
        console.log('Method 3 - SVG found in ref element:', !!svgElement);
      }

      // Method 4: Look for SVG in parent elements
      if (!svgElement) {
        const containers = document.querySelectorAll('.highcharts-container svg');
        if (containers.length > 0) {
          svgElement = containers[0] as SVGElement;
          console.log('Method 4 - SVG found in document:', !!svgElement);
        }
      }

      if (!svgElement) {
        console.error('SVG element not found. Ref details:', {
          current: chartRef.current,
          chart: refElement.chart,
          container: refElement.container,
          hasQuerySelector: !!refElement.querySelector
        });
        throw new Error('SVG element not found');
      }

      console.log('SVG element found:', svgElement.tagName, svgElement.getAttribute('width'), svgElement.getAttribute('height'));

      // Get SVG dimensions
      const svgRect = svgElement.getBoundingClientRect();
      const width = svgRect.width || 800;
      const height = svgRect.height || 400;

      console.log('SVG dimensions:', width, height);

      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Set canvas size (2x for high resolution)
      const scale = 2;
      canvas.width = width * scale;
      canvas.height = height * scale;

      // Scale the context for high resolution
      ctx.scale(scale, scale);

      // Set white background for JPEG
      if (format === 'jpeg') {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, width, height);
      }

      // Clone the SVG to avoid modifying the original
      const svgClone = svgElement.cloneNode(true) as SVGElement;

      // Ensure SVG has proper dimensions
      svgClone.setAttribute('width', width.toString());
      svgClone.setAttribute('height', height.toString());

      // Create a data URL from the SVG
      const svgData = new XMLSerializer().serializeToString(svgClone);
      console.log('SVG data length:', svgData.length);

      // Create image and draw to canvas
      const img = new Image();

      const downloadPromise = new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            console.log('Image loaded, drawing to canvas');
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob and download
            canvas.toBlob((blob) => {
              if (blob) {
                const downloadUrl = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `${filename}.${format}`;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(downloadUrl);
                console.log('Download successful');
                resolve();
              } else {
                reject(new Error('Failed to create blob'));
              }
            }, `image/${format}`, format === 'jpeg' ? 0.9 : 1.0);
          } catch (error) {
            reject(error);
          }
        };

        img.onerror = () => {
          reject(new Error('Failed to load SVG image'));
        };
      });

      // Convert SVG to data URL
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;

      await downloadPromise;
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
