import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ 
  chartRef, 
  filename = 'chart',
  className = ''
}) => {
  const handleDownload = (format: 'png' | 'jpeg') => {
    if (chartRef.current && chartRef.current.chart) {
      const chart = chartRef.current.chart;
      
      // Use Highcharts built-in export functionality
      chart.exportChart({
        type: format === 'png' ? 'image/png' : 'image/jpeg',
        filename: filename,
        scale: 2, // Higher resolution
        sourceWidth: chart.chartWidth,
        sourceHeight: chart.chartHeight,
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
