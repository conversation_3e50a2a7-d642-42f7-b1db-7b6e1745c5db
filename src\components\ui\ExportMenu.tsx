import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileImage, Image } from 'lucide-react';
import Highcharts from 'highcharts';

// Import and initialize the exporting module
import ExportingModule from 'highcharts/modules/exporting';
import OfflineExportingModule from 'highcharts/modules/offline-exporting';

// Initialize modules
if (typeof Highcharts === 'object') {
  ExportingModule(Highcharts);
  OfflineExportingModule(Highcharts);
}

interface ExportMenuProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({
  chartRef,
  filename = 'chart',
  className = ''
}) => {

  const handleDownload = (format: 'png' | 'jpeg') => {
    console.log('Download clicked:', format);

    if (!chartRef.current?.chart) {
      console.error('Chart reference not found');
      alert('Chart not ready for export. Please try again.');
      return;
    }

    const chart = chartRef.current.chart;
    console.log('Chart object found:', !!chart);

    try {
      // Use Highcharts built-in export functionality
      chart.exportChart({
        type: format === 'png' ? 'image/png' : 'image/jpeg',
        filename: filename,
        scale: 2, // High resolution
        sourceWidth: chart.chartWidth,
        sourceHeight: chart.chartHeight,
      });

      console.log('Export initiated successfully');
    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleDownload('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          Download as PNG
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleDownload('jpeg')}>
          <Image className="h-4 w-4 mr-2" />
          Download as JPEG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ExportMenu;
