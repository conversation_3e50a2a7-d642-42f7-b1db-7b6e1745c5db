import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface YearFilterProps {
  selectedYear: number;
  onYearChange: (year: number) => void;
  years?: number[];
}

const YearFilter: React.FC<YearFilterProps> = ({ 
  selectedYear, 
  onYearChange, 
  years = [2024, 2025, 2026] // Default to FY24, FY25, FY26
}) => {
  const handleYearChange = (value: string) => {
    onYearChange(parseInt(value));
  };

  const getFYLabel = (year: number): string => {
    return `FY${year.toString().slice(-2)}`;
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium text-gray-700">Fiscal Year:</span>
      <Select value={selectedYear.toString()} onValueChange={handleYearChange}>
        <SelectTrigger className="w-32">
          <SelectValue placeholder="Select FY" />
        </SelectTrigger>
        <SelectContent>
          {years.map((year) => (
            <SelectItem key={year} value={year.toString()}>
              {getFYLabel(year)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default YearFilter;
