import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface DataItem {
  formCategory: number;
  formId: number;
  value: number | string;
  actualTitle: string;
  title: string;
  approverComments: string;
  dateOfApproval: string;
  dcfId: number;
  conversionValue: number | null;
  entity: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
  dataType: number;
  formType: number;
  uniqueId: string;
  locationId: number;
  level: number;
  reporter: string;
  reportedDate: string;
  reporting_period: string;
  rp: string[];
  reviewedDate: string;
  reporterComments: string;
  reviewer: string;
  efValue: number | null;
  submitId: number;
  reviewerComments: string;
  approver: string;
  status: string;
  indicatorTitle: string;
  indicatorId: number;
  indicatorUnit: string;
  indicatorType: number;
  emissionFactorName: string;
  emissionFactorValue: number;
  emissionFactorCo2Value: number;
  emissionFactorCh4Value: number;
  emissionFactorN2oValue: number;
  efkey: string;
  methodology: string;
  computedValue: string | number | null | undefined;
  computedCo2Value: string | number | null | undefined;
  computedCh4Value: string | number | null | undefined;
  computedN2oValue: string | number | null | undefined;
}

interface EnergyPieChartProps {
  data?: DataItem[];
  year?: number; // Target fiscal year (e.g., 2025 for FY25, 2026 for FY26)
  fymonth?: number; // Fiscal year start month (4 = April)
  loading?: boolean; // Loading state
  title?: string; // Optional custom title
}

const EnergyPieChart = ({
  data = [],
  year = 2026,
  fymonth = 4,
  loading = false,
  title
}: EnergyPieChartProps) => {

  // Use the year prop directly as the target fiscal year
  const targetFY = year;

  // Generate dynamic chart title
  const dynamicTitle = title || `Renewable vs Non-Renewable Energy FY'${targetFY.toString().slice(-2)}`;

  // Helper function to safely convert computedValue to number
  const safeParseValue = (item: DataItem): number => {
    // First try computedValue
    let value = item.computedValue;
    if (value !== null && value !== undefined && value !== '' && value !== '-') {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (!isNaN(numValue)) {
        return numValue;
      }
    }

    // If computedValue is not available, use the value field
    if (item.value !== null && item.value !== undefined) {
      return typeof item.value === 'number' ? item.value : parseFloat(item.value.toString());
    }

    return 0;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    // For fymonth=4 (April start):
    // Apr 2024 to Mar 2025 = FY25
    // Apr 2025 to Mar 2026 = FY26
    if (month >= fyStartMonth) {
      return year + 1; // Apr-Dec of year X belongs to FY(X+1)
    } else {
      return year; // Jan-Mar of year X belongs to FY(X)
    }
  };

  // targetFY is already calculated above

  const filteredData = data.filter(item => {
    if (!item.rp || item.rp.length === 0) return false;

    // Check if any month in the reporting period belongs to target FY
    return item.rp.some(monthStr => {
      const fy = getFiscalYear(monthStr, fymonth);
      return fy === targetFY;
    });
  });

  // Process data to categorize as Renewable vs Non-Renewable
  const processEnergyData = () => {
    let renewableTotal = 0;
    let nonRenewableTotal = 0;

    filteredData.forEach((item, index) => {
      const value = safeParseValue(item);

      // Check if title contains "non" (case insensitive) for Non-Renewable
      const isNonRenewable = item.title && item.title.toLowerCase().includes('non');
      const isRenewable = item.title && item.title.toLowerCase().includes('ren');

      if (isNonRenewable) {
        nonRenewableTotal += value;
      } else if(isRenewable) {
        renewableTotal += value;
      }
    });

    return [
      {
        name: 'Renewable',
        y: renewableTotal,
        color: '#75c3b3' // Using your specified color
      },
      {
        name: 'Non-Renewable',
        y: nonRenewableTotal,
        color: '#ec5624' // Using your specified color
      }
    ].filter(item => item.y > 0); // Only include categories with data
  };

  // Handle loading state
  if (loading) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-center">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-12 h-12">
                <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
                <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
                <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
              </div>
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle no data state
  if (!data || data.length === 0 || filteredData.length === 0) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-center">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                </svg>
              </div>
              <p className="text-gray-500 text-lg font-medium">No data to show</p>
              <p className="text-gray-400 text-sm mt-2">No energy data available for the selected fiscal year</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const processedData = processEnergyData();

  const options: Highcharts.Options = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 500,
    },
    title: {
      text: '',
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>Value: <b>{point.y:.3f}</b>',
    },
    accessibility: {
      point: {
        valueSuffix: ''
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            color: 'hsl(var(--foreground))',
            fontSize: '12px',
            fontWeight: '500'
          }
        },
        showInLegend: true,
        colors: ['#75c3b3', '#ec5624'] // Using your specified colors
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal',
      itemStyle: {
        color: 'hsl(var(--foreground))',
        fontSize: '12px'
      }
    },
    series: [{
      name: 'Energy Type',
      colorByPoint: true,
      data: processedData
    } as any],
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-center">{dynamicTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default EnergyPieChart;