import React from 'react';
import { EnergyEmissionsRatingsChart, Scope2EmissionsNalagarhChart } from '../components/charts/EnergyEmissionsRatingsChart';
import { DataItem } from '../utils/chartDataProcessor';

// Example data structure based on your specification
const exampleData: DataItem[] = [
  {
    "formCategory": 1,
    "formId": 15,
    "value": 8.69,
    "actualTitle": "by Fuel > Petrol",
    "title": "by Fuel > Petrol",
    "approverComments": "No Comments",
    "dateOfApproval": "-",
    "dcfId": 15,
    "conversionValue": null,
    "entity": "Hosur",
    "periodFrom": "07-2024",
    "periodTo": "09-2024",
    "unitOfMeasure": "Litres",
    "dataType": 1,
    "formType": 2,
    "uniqueId": "4-19-75",
    "locationId": 130,
    "level": 2,
    "reporter": "aswini",
    "reportedDate": "3/27/2025",
    "reporting_period": "Jul-2024 to Sep-2024",
    "rp": [
      "07-2024",
      "08-2024",
      "09-2024"
    ],
    "reviewedDate": "6/15/2025",
    "reporterComments": "As per Fuel Usage MIS",
    "reviewer": "Self",
    "efValue": null,
    "submitId": 5655,
    "reviewerComments": "No Comments",
    "approver": "N/A",
    "status": "Under Approval",
    "indicatorTitle": "162 : Total Scope 1 emissions",
    "indicatorId": 162,
    "indicatorUnit": "tCO2e",
    "indicatorType": 2,
    "emissionFactorName": "DEFRA",
    "emissionFactorValue": 2.35372,
    "emissionFactorCo2Value": 2.33955,
    "emissionFactorCh4Value": 0.0082,
    "emissionFactorN2oValue": 0.00597,
    "efkey": "S1-T21-G1-GS1-I1-4-19-75",
    "methodology": "1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000",
    "computedValue": "0.020",
    "computedCo2Value": "0.020",
    "computedCh4Value": "0.000",
    "computedN2oValue": "0.000"
  },
  {
    "formCategory": 1,
    "formId": 16,
    "value": 12.45,
    "actualTitle": "by Fuel > Diesel",
    "title": "by Fuel > Diesel",
    "approverComments": "No Comments",
    "dateOfApproval": "-",
    "dcfId": 16,
    "conversionValue": null,
    "entity": "Hosur",
    "periodFrom": "10-2024",
    "periodTo": "12-2024",
    "unitOfMeasure": "Litres",
    "dataType": 1,
    "formType": 2,
    "uniqueId": "4-19-76",
    "locationId": 130,
    "level": 2,
    "reporter": "aswini",
    "reportedDate": "3/27/2025",
    "reporting_period": "Oct-2024 to Dec-2024",
    "rp": [
      "10-2024",
      "11-2024",
      "12-2024"
    ],
    "reviewedDate": "6/15/2025",
    "reporterComments": "As per Fuel Usage MIS",
    "reviewer": "Self",
    "efValue": null,
    "submitId": 5656,
    "reviewerComments": "No Comments",
    "approver": "N/A",
    "status": "Under Approval",
    "indicatorTitle": "162 : Total Scope 1 emissions",
    "indicatorId": 162,
    "indicatorUnit": "tCO2e",
    "indicatorType": 2,
    "emissionFactorName": "DEFRA",
    "emissionFactorValue": 2.68,
    "emissionFactorCo2Value": 2.65,
    "emissionFactorCh4Value": 0.015,
    "emissionFactorN2oValue": 0.015,
    "efkey": "S1-T21-G1-GS1-I1-4-19-76",
    "methodology": "1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000",
    "computedValue": "0.033",
    "computedCo2Value": "0.033",
    "computedCh4Value": "0.000",
    "computedN2oValue": "0.000"
  },
  // Example with null computedValue (should be treated as 0)
  {
    "formCategory": 1,
    "formId": 17,
    "value": 0,
    "actualTitle": "by Fuel > CNG",
    "title": "by Fuel > CNG",
    "approverComments": "No Comments",
    "dateOfApproval": "-",
    "dcfId": 17,
    "conversionValue": null,
    "entity": "Hosur",
    "periodFrom": "01-2025",
    "periodTo": "03-2025",
    "unitOfMeasure": "Litres",
    "dataType": 1,
    "formType": 2,
    "uniqueId": "4-19-77",
    "locationId": 130,
    "level": 2,
    "reporter": "aswini",
    "reportedDate": "3/27/2025",
    "reporting_period": "Jan-2025 to Mar-2025",
    "rp": [
      "01-2025",
      "02-2025",
      "03-2025"
    ],
    "reviewedDate": "6/15/2025",
    "reporterComments": "No data available",
    "reviewer": "Self",
    "efValue": null,
    "submitId": 5657,
    "reviewerComments": "No Comments",
    "approver": "N/A",
    "status": "Under Approval",
    "indicatorTitle": "162 : Total Scope 1 emissions",
    "indicatorId": 162,
    "indicatorUnit": "tCO2e",
    "indicatorType": 2,
    "emissionFactorName": "DEFRA",
    "emissionFactorValue": 1.85,
    "emissionFactorCo2Value": 1.82,
    "emissionFactorCh4Value": 0.015,
    "emissionFactorN2oValue": 0.015,
    "efkey": "S1-T21-G1-GS1-I1-4-19-77",
    "methodology": "1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000",
    "computedValue": null, // This will be treated as 0
    "computedCo2Value": null,
    "computedCh4Value": null,
    "computedN2oValue": null
  }
];

// Example Scope 2 data
const scope2ExampleData: DataItem[] = [
  {
    "formCategory": 2,
    "formId": 25,
    "value": 150.5,
    "actualTitle": "Electricity Consumption",
    "title": "Electricity Consumption",
    "approverComments": "No Comments",
    "dateOfApproval": "-",
    "dcfId": 25,
    "conversionValue": null,
    "entity": "Nalagarh",
    "periodFrom": "04-2024",
    "periodTo": "06-2024",
    "unitOfMeasure": "kWh",
    "dataType": 2,
    "formType": 2,
    "uniqueId": "4-20-85",
    "locationId": 131,
    "level": 2,
    "reporter": "rajesh",
    "reportedDate": "3/27/2025",
    "reporting_period": "Apr-2024 to Jun-2024",
    "rp": [
      "04-2024",
      "05-2024",
      "06-2024"
    ],
    "reviewedDate": "6/15/2025",
    "reporterComments": "Monthly electricity bills",
    "reviewer": "Self",
    "efValue": null,
    "submitId": 5658,
    "reviewerComments": "No Comments",
    "approver": "N/A",
    "status": "Under Approval",
    "indicatorTitle": "163 : Total Scope 2 emissions",
    "indicatorId": 163,
    "indicatorUnit": "tCO2e",
    "indicatorType": 2,
    "emissionFactorName": "CEA",
    "emissionFactorValue": 0.82,
    "emissionFactorCo2Value": 0.82,
    "emissionFactorCh4Value": 0,
    "emissionFactorN2oValue": 0,
    "efkey": "S2-T21-G1-GS1-I1-4-20-85",
    "methodology": "Electricity Consumption * Grid Emission Factor",
    "computedValue": "0.123",
    "computedCo2Value": "0.123",
    "computedCh4Value": "0.000",
    "computedN2oValue": "0.000"
  }
];

const ChartDataExample: React.FC = () => {
  return (
    <div className="p-6 space-y-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">Chart Data Processing Example</h1>
        <p className="text-lg text-gray-600">
          Demonstrating how the charts handle your data structure with computedValue aggregation
        </p>
      </div>

      {/* Scope 1 Emissions Chart */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Scope 1 Emissions Chart</h2>
        <p className="text-gray-600">
          This chart processes the data by:
          <br />• Automatically generates FY24 (column), FY25 (line), FY26 (dynamic line)
          <br />• Summing computedValue by reporting_period within each fiscal year
          <br />• Handling null/undefined/'-'/'' values as 0
          <br />• Creating x-axis based on fiscal year months (Apr-Mar for fymonth=4)
          <br />• Distributing period values across constituent months
        </p>
        <EnergyEmissionsRatingsChart
          region="Global"
          data={exampleData}
          year={2026}  // Max year (FY26)
          fymonth={4}  // April start (Apr 2025 to Mar 2026 for FY26)
        />
      </div>

      {/* Scope 2 Emissions Chart */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Scope 2 Emissions Chart</h2>
        <p className="text-gray-600">
          This chart uses the same fiscal year logic for Scope 2 emissions data.
          <br />• FY26 = Apr 2025 to Mar 2026
          <br />• FY25 = Apr 2024 to Mar 2025
          <br />• FY24 = Apr 2023 to Mar 2024
        </p>
        <Scope2EmissionsNalagarhChart
          data={scope2ExampleData}
          year={2026}  // Max year (FY26)
          fymonth={4}  // April start
        />
      </div>

      {/* Data Structure Information */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-xl font-semibold mb-4">Fiscal Year Data Processing Logic</h3>
        <div className="space-y-2 text-sm">
          <p><strong>Fiscal Year Generation:</strong> Automatically creates FY24 (column), FY25 (line), FY26 (dynamic line)</p>
          <p><strong>Year Parameter:</strong> Max year (e.g., 2026 for FY26)</p>
          <p><strong>FYMonth Parameter:</strong> Fiscal year start month (4 = April)</p>
          <p><strong>Date Range Logic:</strong> fymonth=4 → Apr(year-1) to Mar(year)</p>
          <p><strong>Data Filtering:</strong> Filters data by fiscal year based on rp array dates</p>
          <p><strong>computedValue handling:</strong> null, undefined, '', '-' → 0</p>
          <p><strong>Aggregation:</strong> Sum computedValue by reporting_period within each FY</p>
          <p><strong>X-axis:</strong> Fiscal year months (Apr-Mar for fymonth=4)</p>
          <p><strong>Distribution:</strong> Period values divided equally across constituent months</p>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded">
          <h4 className="font-semibold text-blue-800 mb-2">Example: year=2026, fymonth=4</h4>
          <div className="text-xs space-y-1 text-blue-700">
            <p>• FY26: Apr 2025 to Mar 2026 (current year data)</p>
            <p>• FY25: Apr 2024 to Mar 2025 (previous year line)</p>
            <p>• FY24: Apr 2023 to Mar 2024 (total column)</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartDataExample;
