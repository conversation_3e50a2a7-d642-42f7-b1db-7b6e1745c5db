// Utility functions for processing chart data

export interface DataItem {
  formCategory: number;
  formId: number;
  value: number;
  actualTitle: string;
  title: string;
  approverComments: string;
  dateOfApproval: string;
  dcfId: number;
  conversionValue: number | null;
  entity: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
  dataType: number;
  formType: number;
  uniqueId: string;
  locationId: number;
  level: number;
  reporter: string;
  reportedDate: string;
  reporting_period: string;
  rp: string[];
  reviewedDate: string;
  reporterComments: string;
  reviewer: string;
  efValue: number | null;
  submitId: number;
  reviewerComments: string;
  approver: string;
  status: string;
  indicatorTitle: string;
  indicatorId: number;
  indicatorUnit: string;
  indicatorType: number;
  emissionFactorName: string;
  emissionFactorValue: number;
  emissionFactorCo2Value: number;
  emissionFactorCh4Value: number;
  emissionFactorN2oValue: number;
  efkey: string;
  methodology: string;
  computedValue: string | number | null | undefined;
  computedCo2Value: string | number | null | undefined;
  computedCh4Value: string | number | null | undefined;
  computedN2oValue: string | number | null | undefined;
}

/**
 * Safely converts computedValue to number, handling various null/undefined/empty cases
 */
export const safeParseValue = (value: string | number | null | undefined): number => {
  if (value === null || value === undefined || value === '' || value === '-') {
    return 0;
  }
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return isNaN(numValue) ? 0 : numValue;
};

/**
 * Processes data to aggregate computedValue by reporting_period
 */
export const aggregateDataByPeriod = (data: DataItem[]): Map<string, number> => {
  const periodMap = new Map<string, number>();
  
  data.forEach(item => {
    const value = safeParseValue(item.computedValue);
    const period = item.reporting_period;
    
    if (periodMap.has(period)) {
      periodMap.set(period, periodMap.get(period)! + value);
    } else {
      periodMap.set(period, value);
    }
  });

  return periodMap;
};

/**
 * Extracts all unique months from the data based on the rp array
 */
export const extractUniqueMonths = (data: DataItem[]): string[] => {
  const allMonths = new Set<string>();
  
  data.forEach(item => {
    if (item.rp && Array.isArray(item.rp)) {
      item.rp.forEach(month => allMonths.add(month));
    }
  });
  
  return Array.from(allMonths).sort();
};

/**
 * Creates chart data by distributing period values across their constituent months
 */
export const createChartDataFromPeriods = (
  data: DataItem[], 
  months: string[]
): number[] => {
  const periodMap = aggregateDataByPeriod(data);
  const allPeriods = Array.from(periodMap.keys());
  
  return months.map(month => {
    let totalValue = 0;
    
    // Sum values for this month across all relevant periods
    allPeriods.forEach(period => {
      const dataItem = data.find(item => item.reporting_period === period);
      const monthsInPeriod = dataItem?.rp || [];
      
      if (monthsInPeriod.includes(month)) {
        const periodValue = periodMap.get(period) || 0;
        // Distribute period value equally across its months
        totalValue += periodValue / monthsInPeriod.length;
      }
    });
    
    return totalValue;
  });
};

/**
 * Formats month strings for display (e.g., "07-2024" -> "Jul 2024")
 */
export const formatMonthForDisplay = (monthStr: string): string => {
  if (!monthStr || !monthStr.includes('-')) return monthStr;
  
  const [month, year] = monthStr.split('-');
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  const monthIndex = parseInt(month) - 1;
  if (monthIndex >= 0 && monthIndex < 12) {
    return `${monthNames[monthIndex]} ${year}`;
  }
  
  return monthStr;
};

/**
 * Determines fiscal year from date string based on fiscal year start month
 */
export const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
  const [month, year] = dateStr.split('-').map(Number);
  if (month >= fyStartMonth) {
    return year + 1;
  } else {
    return year;
  }
};

/**
 * Generates fiscal year months in display format
 */
export const generateFiscalYearMonths = (startMonth: number): string[] => {
  const months = [
    "Apr", "May", "Jun", "Jul", "Aug", "Sep",
    "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
  ];

  // Reorder months based on fiscal year start
  const fiscalMonths = [];
  for (let i = 0; i < 12; i++) {
    const monthIndex = (startMonth - 1 + i) % 12;
    fiscalMonths.push(months[monthIndex]);
  }
  return fiscalMonths;
};

/**
 * Generates month keys in MM-YYYY format for a specific fiscal year
 */
export const generateFYMonthKeys = (fy: number, startMonth: number): string[] => {
  const months = [];
  for (let i = 0; i < 12; i++) {
    const monthNum = ((startMonth - 1 + i) % 12) + 1;
    const year = monthNum >= startMonth ? fy - 1 : fy;
    months.push(`${monthNum.toString().padStart(2, '0')}-${year}`);
  }
  return months;
};

/**
 * Filters data by fiscal year
 */
export const filterDataByFiscalYear = (data: DataItem[], targetFY: number, fyStartMonth: number): DataItem[] => {
  return data.filter(item => {
    if (!item.rp || item.rp.length === 0) return false;

    // Check if any month in the reporting period belongs to target FY
    return item.rp.some(monthStr => {
      const fy = getFiscalYear(monthStr, fyStartMonth);
      return fy === targetFY;
    });
  });
};

/**
 * Example usage and data structure validation
 */
export const validateDataStructure = (data: any[]): DataItem[] => {
  return data.filter(item => {
    // Basic validation - ensure required fields exist
    return (
      item &&
      typeof item.reporting_period === 'string' &&
      Array.isArray(item.rp) &&
      item.computedValue !== undefined
    );
  }) as DataItem[];
};
