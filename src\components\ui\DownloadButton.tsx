import React from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface DownloadButtonProps {
  chartRef: React.RefObject<any>;
  filename?: string;
  className?: string;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ 
  chartRef, 
  filename = 'chart',
  className = ''
}) => {
  const handleDownload = () => {
    if (chartRef.current && chartRef.current.chart) {
      const chart = chartRef.current.chart;
      
      // Use Highcharts built-in export functionality
      chart.exportChart({
        type: 'image/png',
        filename: filename,
        scale: 2, // Higher resolution
        sourceWidth: chart.chartWidth,
        sourceHeight: chart.chartHeight,
      });
    }
  };

  return (
    <Button
      onClick={handleDownload}
      variant="outline"
      size="sm"
      className={`flex items-center gap-2 ${className}`}
    >
      <Download className="h-4 w-4" />
      Download PNG
    </Button>
  );
};

export default DownloadButton;
