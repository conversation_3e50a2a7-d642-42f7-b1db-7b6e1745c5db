# Fiscal Year Chart Implementation

## Overview

The chart components now automatically generate three fiscal years of data:
- **FY24** (two years ago) - Column chart
- **FY25** (previous year) - Grey line
- **FY26** (current year) - Dynamic colored line (from your data)

## Parameters

### `year` (number, default: 2026)
The maximum fiscal year. This represents the current fiscal year (FY26 in the example).

### `fymonth` (number, default: 4)
The starting month of the fiscal year:
- `4` = April (Apr to Mar fiscal year)
- `1` = January (Jan to Dec fiscal year)
- `7` = July (Jul to Jun fiscal year)

## Fiscal Year Logic

### Date Range Calculation
For `year=2026` and `fymonth=4`:
- **FY26**: Apr 2025 to Mar 2026
- **FY25**: Apr 2024 to Mar 2025  
- **FY24**: Apr 2023 to Mar 2024

### Formula
```
If fymonth = 4 (April):
  FY26 = Apr (2026-1) to Mar 2026 = Apr 2025 to Mar 2026
  FY25 = Apr (2025-1) to Mar 2025 = Apr 2024 to Mar 2025
  FY24 = Apr (2024-1) to Mar 2024 = Apr 2023 to Mar 2024
```

## Data Processing

### 1. Data Filtering by Fiscal Year
```typescript
// Helper function to determine fiscal year from date string
const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
  const [month, year] = dateStr.split('-').map(Number);
  if (month >= fyStartMonth) {
    return year + 1;
  } else {
    return year;
  }
};

// Filter data by fiscal year
const filterDataByFY = (targetFY: number): DataItem[] => {
  return data.filter(item => {
    if (!item.rp || item.rp.length === 0) return false;
    
    // Check if any month in the reporting period belongs to target FY
    return item.rp.some(monthStr => {
      const fy = getFiscalYear(monthStr, fymonth);
      return fy === targetFY;
    });
  });
};
```

### 2. Value Aggregation
- Groups data by `reporting_period` within each fiscal year
- Sums `computedValue` for each period
- Handles null/undefined/'-'/'' as 0

### 3. Month Distribution
- Distributes period values equally across constituent months
- Uses the `rp` array to determine which months belong to each period

## Usage Examples

### Basic Usage
```tsx
<EnergyEmissionsRatingsChart 
  region="Global"  
  data={indicatorData.filter(item => item.indicatorId === 162)}
  year={2026}  // FY26 is the current year
  fymonth={4}  // April to March fiscal year
/>

<Scope2EmissionsNortonChart
  data={indicatorData.filter(item => item.indicatorId === 163)}
  year={2026}  // FY26 is the current year
  fymonth={4}  // April to March fiscal year
/>
```

### Different Fiscal Year Starts
```tsx
// Calendar year (Jan-Dec)
<EnergyEmissionsRatingsChart 
  data={data}
  year={2026}
  fymonth={1}  // January to December
/>

// July fiscal year (Jul-Jun)
<EnergyEmissionsRatingsChart 
  data={data}
  year={2026}
  fymonth={7}  // July to June
/>
```

## Data Structure Requirements

Your data must include:
```typescript
{
  "reporting_period": "Jul-2024 to Sep-2024",
  "rp": ["07-2024", "08-2024", "09-2024"],
  "computedValue": "0.020", // or number, null, undefined, '-', ''
  // ... other fields
}
```

### Key Fields:
- **`reporting_period`**: Human-readable period description
- **`rp`**: Array of month strings in "MM-YYYY" format
- **`computedValue`**: Numeric value to aggregate (handles various null cases)

## Chart Output

### X-Axis
- Shows fiscal year months in order (e.g., Apr, May, Jun, ..., Mar for fymonth=4)
- Includes a total column for FY24

### Series
1. **FY24 Total** (Column): Aggregated total for two years ago
2. **FY25** (Grey Line): Monthly data for previous year
3. **FY26** (Dynamic Line): Monthly data for current year with color coding

### Color Logic
- **Green**: Current year value ≤ previous year value (improvement)
- **Red**: Current year value > previous year value (increase)

## Automatic Features

1. **Empty Data Handling**: If no data exists for FY25 or FY24, shows zeros
2. **Partial Year Support**: Automatically limits current year data to current month
3. **Flexible Periods**: Supports quarterly, monthly, or custom reporting periods
4. **Dynamic Scaling**: Y-axis adjusts based on actual data ranges

## Migration from Old Implementation

### Before
```tsx
// Old hardcoded approach
const fy25Data = [379.785, 372.832, 358.776, ...];
const fy26Data = [370.7, 470.5];
```

### After
```tsx
// New dynamic approach
<EnergyEmissionsRatingsChart 
  data={yourActualData}
  year={2026}
  fymonth={4}
/>
```

The component now automatically:
- Filters your data by fiscal year
- Aggregates values by period
- Distributes across months
- Generates all three fiscal years

## Benefits

1. **Data-Driven**: Uses your actual data instead of hardcoded values
2. **Flexible**: Supports any fiscal year start month
3. **Automatic**: Generates historical years automatically
4. **Consistent**: Same logic across all chart components
5. **Maintainable**: No need to update hardcoded arrays
