import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';


import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRef } from 'react';

interface WasteDataItem {
  indicatorId: string;
  indicatorUnit: string;
  title: string;
  actualTitle: string;
  disposalMethod: string;
  computedValue: string;
  value: string;
  rp: string[];
  reporting_period: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
}

interface HazardousWasteCategoryChartProps {
  data?: WasteDataItem[];
  year?: number;
  fymonth?: number;
  loading?: boolean;
}

// Helper function to get current fiscal year
const getCurrentFiscalYear = (fyStartMonth: number): number => {
  const now = new Date();
  const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11
  const currentYear = now.getFullYear();

  if (currentMonth >= fyStartMonth) {
    return currentYear + 1;
  } else {
    return currentYear;
  }
};

const HazardousWasteCategoryChart: React.FC<HazardousWasteCategoryChartProps> = ({
  data = [],
  year = getCurrentFiscalYear(4), // Default to current FY (FY26 as of July 2025)
  fymonth = 4,
  loading = false
}) => {
  const chartRef = useRef<HighchartsReact.RefObject>(null);

  // Helper function to safely convert value to number
  const safeParseValue = (item: WasteDataItem): number => {
    let value = item.computedValue;
    if (value !== null && value !== undefined && value !== '' && value !== '-') {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (!isNaN(numValue)) {
        return numValue;
      }
    }
    if (item.value !== null && item.value !== undefined) {
      return typeof item.value === 'number' ? item.value : parseFloat(item.value.toString());
    }
    return 0;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Generate fiscal year months based on fymonth parameter
  const generateFiscalYearMonths = (startMonth: number): string[] => {
    const months = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
    const startIndex = startMonth - 1;
    return [...months.slice(startIndex), ...months.slice(0, startIndex)];
  };

  const fiscalMonths = generateFiscalYearMonths(fymonth);

  // Filter data for the target fiscal year and only Hazardous waste
  const targetFYData = data.filter(item => {
    if (!item.rp || item.rp.length === 0) return false;

    // Only include Hazardous waste
    const isHazardous = item.actualTitle && item.actualTitle.toLowerCase().startsWith('hazardous >');
    if (!isHazardous) return false;

    return item.rp.some(monthStr => {
      const fy = getFiscalYear(monthStr, fymonth);
      return fy === year;
    });
  });

  // Handle loading state
  if (loading) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Hazardous Waste Generation By Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-12 h-12">
                <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
                <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
                <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
              </div>
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle no data state
  if (!data || data.length === 0 || targetFYData.length === 0) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Hazardous Waste Generation By Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <p className="text-gray-500 text-lg font-medium">No data to show</p>
              <p className="text-gray-400 text-sm mt-2">No hazardous waste data available for the selected period</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Process data by month and category
  const processDataByCategory = () => {
    const categoryData: { [key: string]: number[] } = {};

    // Generate month keys for the target fiscal year
    const generateMonthKeys = (fy: number, startMonth: number): string[] => {
      const keys = [];
      for (let i = 0; i < 12; i++) {
        const month = ((startMonth - 1 + i) % 12) + 1;
        const year = month >= startMonth ? fy - 1 : fy;
        keys.push(`${month.toString().padStart(2, '0')}-${year}`);
      }
      return keys;
    };

    const monthKeys = generateMonthKeys(year, fymonth);

    // Initialize categories based on the title field
    const categories = [...new Set(targetFYData.map(item => item.title))];
    categories.forEach(category => {
      categoryData[category] = [];
    });

    monthKeys.forEach(monthKey => {
      categories.forEach(category => {
        let categoryTotal = 0;
        let hasData = false;

        targetFYData.forEach(item => {
          if (item.rp && item.rp.includes(monthKey) && item.title === category) {
            const value = safeParseValue(item);
            categoryTotal += value;
            hasData = true;
          }
        });

        categoryData[category].push(hasData ? categoryTotal : null);
      });
    });

    return categoryData;
  };

  const categoryData = processDataByCategory();

  // Generate series from processed category data
  const colors = [
    'hsl(var(--chart-hazardous))',
    'hsl(0, 70%, 60%)',
    'hsl(20, 70%, 55%)',
    'hsl(40, 70%, 50%)',
    'hsl(60, 70%, 45%)',
    'hsl(80, 70%, 40%)',
    'hsl(100, 70%, 35%)',
    'hsl(120, 70%, 30%)',
    'hsl(140, 70%, 25%)',
    'hsl(160, 70%, 20%)',
    'hsl(180, 70%, 15%)'
  ];

  const series = Object.keys(categoryData).map((category, index) => ({
    name: category,
    data: categoryData[category],
    color: colors[index % colors.length],
    type: 'column' as const
  }));

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 500,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: fiscalMonths,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Waste Generation (MT)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      valueSuffix: ' MT',
      shared: true
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    },
    exporting: {
      enabled: true,
      filename: `hazardous-waste-category-fy${year.toString().slice(-2)}`,
      buttons: {
        contextButton: {
          symbol: 'download',
          text: 'Download',
          menuItems: [
            'downloadPNG',
            'downloadJPEG',
            'separator',
            'downloadSVG'
          ]
        }
      },
      scale: 2,
      sourceWidth: 800,
      sourceHeight: 400
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Hazardous Waste Generation By Category - FY{year.toString().slice(-2)}</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          ref={chartRef}
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default HazardousWasteCategoryChart;