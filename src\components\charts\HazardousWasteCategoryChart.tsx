import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import wasteData from '@/data/wasteData.json';

interface HazardousWasteCategoryChartProps {
  selectedMonth?: string;
}

const HazardousWasteCategoryChart: React.FC<HazardousWasteCategoryChartProps> = ({ selectedMonth = 'all' }) => {
  const { months, categories } = wasteData.hazardousCategory;

  // Filter data based on selected month
  const filteredMonths = selectedMonth === 'all' ? months : [selectedMonth];
  const monthIndex = selectedMonth === 'all' ? -1 : months.indexOf(selectedMonth);
  
  const getFilteredData = (data: number[]) => {
    if (selectedMonth === 'all') return data;
    return monthIndex !== -1 ? [data[monthIndex]] : [];
  };

  const series = [
    {
      name: 'Used Oil',
      data: getFilteredData(categories.usedOil),
      color: 'hsl(var(--chart-hazardous))',
      type: 'column' as const
    },
    {
      name: 'Bio-medical Waste',
      data: getFilteredData(categories.bioMedicalWaste),
      color: 'hsl(0, 70%, 60%)',
      type: 'column' as const
    },
    {
      name: 'Oil Soaked Cotton',
      data: getFilteredData(categories.oilSoakedCotton),
      color: 'hsl(20, 70%, 55%)',
      type: 'column' as const
    },
    {
      name: 'Batteries (Lithium Ion)',
      data: getFilteredData(categories.batteriesLithium),
      color: 'hsl(40, 70%, 50%)',
      type: 'column' as const
    },
    {
      name: 'Waste Thinner',
      data: getFilteredData(categories.wasteThinner),
      color: 'hsl(60, 70%, 45%)',
      type: 'column' as const
    },
    {
      name: 'Batteries (Lead Acid)',
      data: getFilteredData(categories.batteriesLeadAcid),
      color: 'hsl(80, 70%, 40%)',
      type: 'column' as const
    },
    {
      name: 'Paint Sludge',
      data: getFilteredData(categories.paintSludge),
      color: 'hsl(100, 70%, 35%)',
      type: 'column' as const
    },
    {
      name: 'E-waste',
      data: getFilteredData(categories.eWaste),
      color: 'hsl(120, 70%, 30%)',
      type: 'column' as const
    },
    {
      name: 'Empty Containers',
      data: getFilteredData(categories.emptyContainers),
      color: 'hsl(140, 70%, 25%)',
      type: 'column' as const
    },
    {
      name: 'ETP/Chemical Sludge',
      data: getFilteredData(categories.etpChemicalSludge),
      color: 'hsl(160, 70%, 20%)',
      type: 'column' as const
    },
    {
      name: 'Phosphate Sludge',
      data: getFilteredData(categories.phosphateSludge),
      color: 'hsl(180, 70%, 15%)',
      type: 'column' as const
    }
  ];

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 500,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: filteredMonths,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Waste Generation (Tonnes)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      valueSuffix: ' Tonnes',
      shared: true
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Hazardous Waste Generation By Category</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default HazardousWasteCategoryChart;