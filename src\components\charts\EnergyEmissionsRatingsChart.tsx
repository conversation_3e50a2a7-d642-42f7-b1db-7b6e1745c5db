import React, { useState, useRef } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface DataItem {
  formCategory: number;
  formId: number;
  value: number;
  actualTitle: string;
  title: string;
  approverComments: string;
  dateOfApproval: string;
  dcfId: number;
  conversionValue: number | null;
  entity: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
  dataType: number;
  formType: number;
  uniqueId: string;
  locationId: number;
  level: number;
  reporter: string;
  reportedDate: string;
  reporting_period: string;
  rp: string[];
  reviewedDate: string;
  reporterComments: string;
  reviewer: string;
  efValue: number | null;
  submitId: number;
  reviewerComments: string;
  approver: string;
  status: string;
  indicatorTitle: string;
  indicatorId: number;
  indicatorUnit: string;
  indicatorType: number;
  emissionFactorName: string;
  emissionFactorValue: number;
  emissionFactorCo2Value: number;
  emissionFactorCh4Value: number;
  emissionFactorN2oValue: number;
  efkey: string;
  methodology: string;
  computedValue: string | number | null | undefined;
  computedCo2Value: string | number | null | undefined;
  computedCh4Value: string | number | null | undefined;
  computedN2oValue: string | number | null | undefined;
}

interface EnergyEmissionsRatingsChartProps {
  region?: string;
  data?: DataItem[];
  year?: number; // Max year (FY26)
  fymonth?: number; // Fiscal year start month (4 = April)
  loading?: boolean; // Loading state
}

export const EnergyEmissionsRatingsChart: React.FC<
  EnergyEmissionsRatingsChartProps
> = ({ region = "Global", data = [], year = 2026, fymonth = 4, loading = false }) => {
  const chartRef = useRef<any>(null);


  // Helper function to safely convert computedValue to number
  const safeParseValue = (value: string | number | null | undefined): number => {
    if (value === null || value === undefined || value === '' || value === '-') {
      return 0;
    }
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? 0 : numValue;
  };

  // Generate fiscal year months based on fymonth parameter
  const generateFiscalYearMonths = (startMonth: number): string[] => {
    const months = [
      "Apr", "May", "Jun", "Jul", "Aug", "Sep",
      "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
    ];

    // Reorder months based on fiscal year start
    const fiscalMonths = [];
    for (let i = 0; i < 12; i++) {
      const monthIndex = (startMonth - 1 + i) % 12;
      fiscalMonths.push(months[monthIndex]);
    }
    return fiscalMonths;
  };

  // Calculate fiscal years
  const currentFY = year; // FY26
  const previousFY = year - 1; // FY25
  const twoYearsAgoFY = year - 2; // FY24

  // Generate dynamic chart title
  const chartTitle = `Scope 1 Emissions - Norton (FY ${previousFY.toString().slice(-2)} vs FY ${currentFY.toString().slice(-2)} Comparison)`;

  // Generate dynamic series names
  const fy24Label = `FY ${twoYearsAgoFY.toString().slice(-2)}`;
  const fy25Label = `FY ${previousFY.toString().slice(-2)}`;
  const fy26Label = `FY ${currentFY.toString().slice(-2)}`;

  // Generate fiscal year date ranges
  const generateFYDateRange = (fy: number, startMonth: number): { start: Date, end: Date } => {
    const startYear = startMonth <= 3 ? fy : fy - 1;
    const endYear = startMonth <= 3 ? fy + 1 : fy;

    return {
      start: new Date(startYear, startMonth - 1, 1),
      end: new Date(endYear, (startMonth + 11) % 12, 0) // Last day of end month
    };
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Filter data by fiscal year
  const filterDataByFY = (targetFY: number): DataItem[] => {
    return data.filter(item => {
      if (!item.rp || item.rp.length === 0) return false;

      // Check if any month in the reporting period belongs to target FY
      return item.rp.some(monthStr => {
        const fy = getFiscalYear(monthStr, fymonth);
        return fy === targetFY;
      });
    });
  };

  // Get data for each fiscal year
  const fy26DataFiltered = filterDataByFY(currentFY); // Current year data from props
  const fy25DataFiltered = filterDataByFY(previousFY); // Previous year (could be empty)
  const fy24DataFiltered = filterDataByFY(twoYearsAgoFY); // Two years ago (could be empty)

  // Debug logging for EnergyEmissionsRatingsChart
  console.log('=== EnergyEmissionsRatingsChart Debug ===');
  console.log('Raw data length:', data.length);
  console.log('Target years - Current FY:', currentFY, 'Previous FY:', previousFY, 'Two years ago FY:', twoYearsAgoFY);
  console.log('FY26 filtered data length:', fy26DataFiltered.length);
  console.log('FY25 filtered data length:', fy25DataFiltered.length);
  console.log('FY24 filtered data length:', fy24DataFiltered.length);
  if (data.length > 0) {
    console.log('Sample data item:', data[0]);
    console.log('Sample rp field:', data[0].rp);
    console.log('Sample reporting_period:', data[0].reporting_period);
    console.log('Sample computedValue:', data[0].computedValue);
  }

  // Debug the processed data
  if (fy25DataFiltered.length > 0) {
    console.log('FY25 sample items (first 3):');
    fy25DataFiltered.slice(0, 3).forEach((item, index) => {
      console.log(`Item ${index}:`, {
        rp: item.rp,
        reporting_period: item.reporting_period,
        computedValue: item.computedValue
      });
    });
  }

  // Process data for each fiscal year
  const processDataByFY = (fyData: DataItem[]) => {
    const periodMap = new Map<string, number>();

    fyData.forEach(item => {
      const value = safeParseValue(item.computedValue);
      const period = item.reporting_period;

      if (periodMap.has(period)) {
        periodMap.set(period, periodMap.get(period)! + value);
      } else {
        periodMap.set(period, value);
      }
    });

    return periodMap;
  };

  // Create month keys in MM-YYYY format for the fiscal year
  // For year=2026, fymonth=4: Apr-2025 to Mar-2026
  const generateFYMonthKeys = (fy: number, startMonth: number): string[] => {
    const months = [];
    for (let i = 0; i < 12; i++) {
      const monthNum = ((startMonth - 1 + i) % 12) + 1;
      // For fymonth=4: months 4,5,6,7,8,9,10,11,12 are year-1, months 1,2,3 are year
      const year = monthNum >= startMonth ? fy - 1 : fy;
      months.push(`${monthNum.toString().padStart(2, '0')}-${year}`);
    }
    return months;
  };

  const currentFYMonthKeys = generateFYMonthKeys(currentFY, fymonth);
  const previousFYMonthKeys = generateFYMonthKeys(previousFY, fymonth);

  // Generate fiscal year months for display (without years)
  const fiscalMonths = currentFYMonthKeys.map(monthKey => {
    const [month] = monthKey.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthName = monthNames[parseInt(month) - 1];
    return monthName;
  });

  // Debug: Log the fiscal year months being generated
  console.log(`FY${currentFY} months (fymonth=${fymonth}):`, currentFYMonthKeys);
  console.log(`FY${previousFY} months:`, previousFYMonthKeys);

  // Process current year data (FY26) - ensure all 12 months are included
  const fy26ProcessedData = processDataByFY(fy26DataFiltered);
  const fy26ChartData = currentFYMonthKeys.map(monthKey => {
    let totalValue = 0;

    // Find periods that include this month
    fy26DataFiltered.forEach(item => {
      if (item.rp && item.rp.includes(monthKey)) {
        const periodValue = fy26ProcessedData.get(item.reporting_period) || 0;
        totalValue += periodValue / item.rp.length;
      }
    });

    // Return 0 if no data found for this month (not null to avoid chart issues)
    return totalValue || 0;
  });

  // Process previous year data (FY25) - ensure all 12 months are included
  const fy25ProcessedData = processDataByFY(fy25DataFiltered);

  console.log('=== FY25 Monthly Processing Debug ===');
  console.log('FY25 processed periods:', fy25ProcessedData);
  console.log('Total from periods:', Array.from(fy25ProcessedData.values()).reduce((sum, val) => sum + val, 0));

  const fy25ChartData = previousFYMonthKeys.map((monthKey, index) => {
    let totalValue = 0;
    const monthName = ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'][index];
    let itemsFound = 0;

    // Find periods that include this month
    fy25DataFiltered.forEach(item => {
      if (item.rp && item.rp.includes(monthKey)) {
        const periodValue = fy25ProcessedData.get(item.reporting_period) || 0;
        const distributedValue = periodValue / item.rp.length;
        totalValue += distributedValue;
        itemsFound++;

        // Debug first few months
        if (index <= 2) {
          console.log(`${monthName}: Found item with period ${item.reporting_period}, value ${periodValue}, distributed as ${distributedValue}, rp:`, item.rp);
        }
      }
    });

    // Debug first few months
    if (index <= 2) {
      console.log(`${monthName} (${monthKey}): ${itemsFound} items found, total: ${totalValue}`);
    }

    // Return 0 if no data found for this month
    return totalValue || 0;
  });

  // Calculate FY24 total (only from actual data, no default values)
  const fy24ProcessedData = processDataByFY(fy24DataFiltered);
  const calculatedFY24Total = Array.from(fy24ProcessedData.values()).reduce((sum, val) => sum + val, 0);

  // Categories for x-axis - total column + monthly categories
  const totalCategory = [`FY ${twoYearsAgoFY.toString().slice(-2)} Total`];
  const monthlyCategories = fiscalMonths;

  // Get current month to limit FY 26 data to avoid showing future months
  const getCurrentFiscalMonthIndex = (): number => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // 1-12
    const currentYear = currentDate.getFullYear();

    // Find which fiscal month we're currently in
    for (let i = 0; i < currentFYMonthKeys.length; i++) {
      const [month, year] = currentFYMonthKeys[i].split('-').map(Number);
      if (year === currentYear && month === currentMonth) {
        return i;
      }
      // If we're past this fiscal year, return the last month
      if (year > currentYear || (year === currentYear && month > currentMonth)) {
        return Math.max(0, i - 1);
      }
    }
    // If we're past the fiscal year, return all months
    return currentFYMonthKeys.length - 1;
  };

  const currentFiscalMonthIndex = getCurrentFiscalMonthIndex();

  // Use the calculated fiscal year data
  const fy24Total = calculatedFY24Total;
  const fy25Data = fy25ChartData;

  // Limit FY 26 data to current month - set future months to null
  const fy26Data = fy26ChartData.map((value, index) => {
    if (index <= currentFiscalMonthIndex) {
      return value; // Show actual data for current and past months
    } else {
      return null; // Hide future months
    }
  });

  console.log(`Current fiscal month index: ${currentFiscalMonthIndex} (showing data up to ${fiscalMonths[currentFiscalMonthIndex]} - ${currentFYMonthKeys[currentFiscalMonthIndex]})`);

  // Define dynamic labels at component level for all chart configurations
  const fy24LabelGlobal = `FY ${twoYearsAgoFY.toString().slice(-2)}`;
  const fy25LabelGlobal = `FY ${previousFY.toString().slice(-2)}`;
  const fy26LabelGlobal = `FY ${currentFY.toString().slice(-2)}`;

  // Filter state for months
  const [fromMonth, setFromMonth] = useState(monthlyCategories[0]);
  const [toMonth, setToMonth] = useState(monthlyCategories[monthlyCategories.length - 1]);

  // Helper to get filtered indices
  const fromIdx = monthlyCategories.indexOf(fromMonth);
  const toIdx = monthlyCategories.indexOf(toMonth);
  const filteredMonthlyCategories = monthlyCategories.slice(fromIdx, toIdx + 1);

  // Filter FY 25 data (full year available - keep all months)
  const filteredFY25Data = fy25Data.slice(fromIdx, toIdx + 1);

  // Filter FY 26 data (only up to current month, then apply user filter)
  const maxToIdx = Math.min(toIdx, currentFiscalMonthIndex);
  const filteredFY26Data = fy26Data.slice(fromIdx, maxToIdx + 1);

  // Chart categories include total column + all filtered months (show all months for FY25)
  const finalChartCategories = [totalCategory[0], ...filteredMonthlyCategories];

  // Create dynamic color zones for current year line based on comparison with previous year
  const createDynamicZones = () => {
    const zones = [];
    const currentYearData = [null, ...filteredFY26Data]; // null for total column position
    const previousYearData = [null, ...filteredFY25Data.slice(0, filteredFY26Data.length)]; // match length

    for (let i = 1; i < currentYearData.length; i++) { // Start from 1 to skip null
      const currentValue = currentYearData[i];
      const previousValue = previousYearData[i];

      if (currentValue !== null && previousValue !== null) {
        const isImprovement = currentValue <= previousValue; // Lower or equal emissions = better
        zones.push({
          value: i,
          color: isImprovement ? '#22c55e' : '#ef4444', // Green if improved or equal, Red if worse
        });
      }
    }

    // Add final zone to cover the rest
    if (zones.length > 0 && filteredFY26Data.length > 0 && filteredFY25Data.length > 0) {
      const lastCurrentValue = filteredFY26Data[filteredFY26Data.length - 1];
      const lastPreviousValue = filteredFY25Data[filteredFY26Data.length - 1];
      const lastComparison = lastCurrentValue <= lastPreviousValue;
      zones.push({
        value: currentYearData.length,
        color: lastComparison ? '#22c55e' : '#ef4444',
      });
    }

    return zones;
  };

  const dynamicZones = createDynamicZones();



  const options: Highcharts.Options = {
    chart: {
      backgroundColor: "#ffffff",
      style: {
        fontFamily:
          '"Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      height: 450,
      spacingTop: 30,
      spacingBottom: 80,
      spacingLeft: 60,
      spacingRight: 40,
      plotBorderWidth: 0,
      borderRadius: 8,
    },
    title: {
      text: undefined,
    },
    xAxis: {
      categories: finalChartCategories,
      title: {
        text: "",
        style: {
          fontSize: "13px",
          color: "#6B7280",
          fontWeight: "500",
        },
        margin: 20,
      },
      labels: {
        style: {
          fontSize: "12px",
          color: "#6B7280",
          fontWeight: "400",
        },
        rotation: 0,
        y: 25,
        useHTML: true,
        formatter: function () {
          // Show 'FY 24 Total' under the first category
          if (this.pos === 0) {
            return (
              '<span style="display: flex; flex-direction: column; align-items: center;">'
              + '<span style="font-weight: 600; color: #374151; margin-bottom: 2px;">' + this.value + '</span>'
              + '<span style="font-weight: 600; color: #374151;">Reference Year</span>'
              + '</span>'
            );
          }
          // Just return the month name directly for other categories
          return String(this.value);
        },
      },
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
    },
    yAxis: {
      min: 0,
      // Set max dynamically for better fit
      max: Math.ceil(Math.max(fy24Total, ...filteredFY25Data, ...filteredFY26Data) / 100) * 100,
      tickInterval: 100,
      title: {
        text: "tCO₂e (tons of CO₂ equivalent)",
        style: {
          fontSize: "12px",
          color: "#374151",
          fontWeight: "500",
        },
        margin: 30,
        rotation: 270,
      },
      labels: {
        style: {
          fontSize: "12px",
          color: "#6B7280",
          fontWeight: "400",
        },
        x: -15,
        formatter: function () {
          return this.value.toString();
        },
      },
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "bottom",
      itemStyle: {
        fontSize: "12px",
        color: "#374151",
        fontWeight: "500",
      },
      itemHoverStyle: {
        color: "#111827",
      },
      itemMarginTop: 5,
      itemMarginBottom: 5,
      symbolHeight: 12,
      symbolWidth: 20,
      symbolRadius: 2,
      y: 15,
      borderWidth: 0,
    },
    tooltip: {
      shared: true,
      backgroundColor: "#FFFFFF",
      borderColor: "#E5E7EB",
      borderRadius: 8,
      borderWidth: 1,
      shadow: {
        color: "rgba(0, 0, 0, 0.1)",
        offsetX: 0,
        offsetY: 2,
        opacity: 0.1,
        width: 4,
      },
      style: {
        fontSize: "12px",
        color: "#374151",
        fontWeight: "400",
      },
      useHTML: true,
      padding: 12,
      formatter: function () {
        console.log(this)
        let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
        this.points.forEach(function (point) {
          const color = point.series.color;
          s += `<div style="margin-bottom: 4px;">
                  <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                  <span style="font-weight: 600; color: #111827;">${Number(point.y).toFixed(2)} tCO₂e</span>
                </div>`;
        });
        return s;
      },
    },
    plotOptions: {
      column: {
        borderRadius: 3,
        pointPadding: 0.15,
        groupPadding: 0.1,
        borderWidth: 0,
        pointWidth: 40,
        states: {
          hover: {
            brightness: 0.05,
            borderColor: "#374151",
            borderWidth: 1,
          },
        },
      },
      line: {
        marker: {
          enabled: true,
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
      },
    },
    series: [
      {
        type: "column",
        name: `${fy24Label} Total Actual`,
        data: [
          { y: calculatedFY24Total, color: "#9CA3AF" },
          ...Array(filteredMonthlyCategories.length).fill(null),
        ],
        showInLegend: false,
        zIndex: 1,
        borderRadius: 3,
      },
      {
        type: "line",
        name: fy25Label,
        data: [null, ...filteredFY25Data],
        color: "#9CA3AF", // grey for previous year
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 2,
      },
      {
        type: "line",
        name: fy26Label,
        data: [null, ...filteredFY26Data, ...Array(filteredMonthlyCategories.length - filteredFY26Data.length).fill(null)],
        color: "#9CA3AF", // default color, will be overridden by zones
        zones: dynamicZones,
        zoneAxis: 'x',
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
          fillColor: null, // use line color
        },
        lineWidth: 3,
        zIndex: 2,
      },
    ],
    credits: { enabled: false },
  };

  // Handle loading state
  if (loading) {
    const loadingTitle = `Scope 1 Emissions - Norton (FY ${(year - 1).toString().slice(-2)} vs FY ${year.toString().slice(-2)} Comparison)`;
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {loadingTitle}
              </h3>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white flex items-center justify-center h-96">
          <div className="text-center">
            <div className="relative mx-auto mb-4 w-12 h-12">
              <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
              <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
            </div>
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Handle no data state
  if (!data || data.length === 0) {
    const noDataTitle = `Scope 1 Emissions - Norton (FY ${(year - 1).toString().slice(-2)} vs FY ${year.toString().slice(-2)} Comparison)`;
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {noDataTitle}
              </h3>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white flex items-center justify-center h-96">
          <div className="text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-gray-500 text-lg font-medium">No data to show</p>
            <p className="text-gray-400 text-sm mt-2">No emissions data available for the selected criteria</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          {/* Chart Title and Details - now on the left */}
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {chartTitle}
            </h3>
            {/* <div className="flex items-center space-x-6 text-sm text-gray-600">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                MP/CP ref: MPCP/Sustainability
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                UOM: No of ratings
              </span>
            </div> */}
          </div>


        </div>
      </div>

      {/* Filter Controls and Results Row */}
      <div className="flex flex-row justify-between items-end px-6 pt-4 gap-8">
        <div className="flex flex-row items-center gap-6" style={{ display: 'none' }} >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">From</span>
            <select
              className="border rounded px-2 py-1"
              value={fromMonth}
              onChange={e => {
                const val = e.target.value;
                setFromMonth(val);
                if (monthlyCategories.indexOf(val) > monthlyCategories.indexOf(toMonth)) {
                  setToMonth(val);
                }
              }}
            >
              {monthlyCategories.map((month, idx) => (
                <option key={month} value={month} disabled={idx > monthlyCategories.indexOf(toMonth)}>
                  {month}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">To</span>
            <select
              className="border rounded px-2 py-1"
              value={toMonth}
              onChange={e => {
                const val = e.target.value;
                setToMonth(val);
                if (monthlyCategories.indexOf(val) < monthlyCategories.indexOf(fromMonth)) {
                  setFromMonth(val);
                }
              }}
            >
              {monthlyCategories.map((month, idx) => (
                <option key={month} value={month} disabled={idx < monthlyCategories.indexOf(fromMonth)}>
                  {month}
                </option>
              ))}
            </select>
          </div>
        </div>
        {/* Filtered Results Row */}
        <div className="flex flex-row gap-6 justify-content-end">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-gray-900 mr-2">{fy25Label} Total (YTD):</span> {filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className="bg-blue-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-blue-900 mr-2">{fy26Label} Total (YTD):</span> {filteredFY26Data.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className={`rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) <= 0
              ? 'bg-green-100'
              : 'bg-red-100'
            }`}>
            <span className={`font-semibold mr-2 ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) <= 0
                ? 'text-green-900'
                : 'text-red-900'
              }`}>
              {(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) <= 0 ? 'Improvement:' : 'Increase:'}
            </span>
            {Math.abs(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)).toFixed(3)} tCO₂e
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="p-6 bg-white">
        <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500 font-medium">
          {" "}
          Green Line: Within baseline target  | Red Line: Above baseline target
        </p>
      </div>
    </div>
  );
};

// Scope 2 Emissions - Norton Chart
interface Scope2EmissionsNortonChartProps {
  data?: DataItem[];
  year?: number; // Max year (FY26)
  fymonth?: number; // Fiscal year start month (4 = April)
  loading?: boolean; // Loading state
}

export const Scope2EmissionsNortonChart: React.FC<Scope2EmissionsNortonChartProps> = ({ data = [], year = 2026, fymonth = 4, loading = false }) => {
  const chartRef = useRef<any>(null);
  // Helper function to safely convert computedValue to number
  const safeParseValue = (value: string | number | null | undefined): number => {
    if (value === null || value === undefined || value === '' || value === '-') {
      return 0;
    }
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? 0 : numValue;
  };

  // Generate fiscal year months based on fymonth parameter
  const generateFiscalYearMonths = (startMonth: number): string[] => {
    const months = [
      "Apr", "May", "Jun", "Jul", "Aug", "Sep",
      "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
    ];

    // Reorder months based on fiscal year start
    const fiscalMonths = [];
    for (let i = 0; i < 12; i++) {
      const monthIndex = (startMonth - 1 + i) % 12;
      fiscalMonths.push(months[monthIndex]);
    }
    return fiscalMonths;
  };

  // Calculate fiscal years
  const currentFY = year; // FY26
  const previousFY = year - 1; // FY25
  const twoYearsAgoFY = year - 2; // FY24

  // Generate dynamic chart title
  const chartTitle = `Scope 2 Emissions - Norton (FY ${previousFY.toString().slice(-2)} vs FY ${currentFY.toString().slice(-2)} Comparison)`;

  // Generate dynamic series names
  const fy23Label = `FY ${twoYearsAgoFY.toString().slice(-2)}`;  // year-2
  const fy24Label = `FY ${previousFY.toString().slice(-2)}`;     // year-1
  const fy25Label = `FY ${currentFY.toString().slice(-2)}`;      // year

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Filter data by fiscal year
  const filterDataByFY = (targetFY: number): DataItem[] => {
    return data.filter(item => {
      if (!item.rp || item.rp.length === 0) return false;

      // Check if any month in the reporting period belongs to target FY
      return item.rp.some(monthStr => {
        const fy = getFiscalYear(monthStr, fymonth);
        return fy === targetFY;
      });
    });
  };

  // Get data for each fiscal year
  const fy25DataFiltered = filterDataByFY(currentFY);    // year (current)
  const fy24DataFiltered = filterDataByFY(previousFY);   // year-1 (previous)
  const fy23DataFiltered = filterDataByFY(twoYearsAgoFY); // year-2 (two years ago)

  // Check if we have any relevant data after filtering
  const hasAnyData = fy25DataFiltered.length > 0 || fy24DataFiltered.length > 0 || fy23DataFiltered.length > 0;



  // Process data for each fiscal year
  const processDataByFY = (fyData: DataItem[]) => {
    const periodMap = new Map<string, number>();

    fyData.forEach(item => {
      const value = safeParseValue(item.computedValue);
      const period = item.reporting_period;

      if (periodMap.has(period)) {
        periodMap.set(period, periodMap.get(period)! + value);
      } else {
        periodMap.set(period, value);
      }
    });

    return periodMap;
  };

  // Create month keys in MM-YYYY format for the fiscal year
  // For year=2026, fymonth=4: Apr-2025 to Mar-2026
  const generateFYMonthKeys = (fy: number, startMonth: number): string[] => {
    const months = [];
    for (let i = 0; i < 12; i++) {
      const monthNum = ((startMonth - 1 + i) % 12) + 1;
      // For fymonth=4: months 4,5,6,7,8,9,10,11,12 are year-1, months 1,2,3 are year
      const year = monthNum >= startMonth ? fy - 1 : fy;
      months.push(`${monthNum.toString().padStart(2, '0')}-${year}`);
    }
    return months;
  };

  const currentFYMonthKeys = generateFYMonthKeys(currentFY, fymonth);
  const previousFYMonthKeys = generateFYMonthKeys(previousFY, fymonth);

  // Generate fiscal year months for display (without years)
  const fiscalMonths = currentFYMonthKeys.map(monthKey => {
    const [month] = monthKey.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthName = monthNames[parseInt(month) - 1];
    return monthName;
  });

  // Debug: Log the fiscal year months being generated
  console.log(`Scope2 FY${currentFY} months (fymonth=${fymonth}):`, currentFYMonthKeys);
  console.log(`Scope2 FY${previousFY} months:`, previousFYMonthKeys);

  // Process current year data (FY25) - ensure all 12 months are included
  const fy25ProcessedData = processDataByFY(fy25DataFiltered);

  const fy25TotalFromProcessed = Array.from(fy25ProcessedData.values()).reduce((sum, val) => sum + val, 0);

  const fy25ChartData = currentFYMonthKeys.map(monthKey => {
    let totalValue = 0;

    // Find periods that include this month
    fy25DataFiltered.forEach(item => {
      if (item.rp && item.rp.includes(monthKey)) {
        const periodValue = fy25ProcessedData.get(item.reporting_period) || 0;
        totalValue += periodValue / item.rp.length;
      }
    });

    // Return 0 if no data found for this month (not null to avoid chart issues)
    return totalValue || 0;
  });

  // Process previous year data (FY24) - ensure all 12 months are included
  const fy24ProcessedData = processDataByFY(fy24DataFiltered);
  const fy24ChartData = previousFYMonthKeys.map(monthKey => {
    let totalValue = 0;

    // Find periods that include this month
    fy24DataFiltered.forEach(item => {
      if (item.rp && item.rp.includes(monthKey)) {
        const periodValue = fy24ProcessedData.get(item.reporting_period) || 0;
        totalValue += periodValue / item.rp.length;
      }
    });

    // Return 0 if no data found for this month
    return totalValue || 0;
  });

  // Calculate FY23 total (only from actual data, no default values)
  const fy23ProcessedData = processDataByFY(fy23DataFiltered);
  const fy23Total = Array.from(fy23ProcessedData.values()).reduce((sum, val) => sum + val, 0);

  // Categories for x-axis - total column + monthly categories
  const totalCategory = [`FY ${twoYearsAgoFY.toString().slice(-2)} Total`];
  const monthlyCategories = fiscalMonths;

  // Get current month to limit FY 26 data to avoid showing future months
  const getCurrentFiscalMonthIndex = (): number => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // 1-12
    const currentYear = currentDate.getFullYear();

    // Find which fiscal month we're currently in
    for (let i = 0; i < currentFYMonthKeys.length; i++) {
      const [month, year] = currentFYMonthKeys[i].split('-').map(Number);
      if (year === currentYear && month === currentMonth) {
        return i;
      }
      // If we're past this fiscal year, return the last month
      if (year > currentYear || (year === currentYear && month > currentMonth)) {
        return Math.max(0, i - 1);
      }
    }
    // If we're past the fiscal year, return all months
    return currentFYMonthKeys.length - 1;
  };

  const currentFiscalMonthIndex = getCurrentFiscalMonthIndex();

  // Use the calculated fiscal year data
  const fy23TotalValue = fy23Total;
  const fy24Data = fy24ChartData;

  // Limit FY 25 data to current month - set future months to null
  const fy25Data = fy25ChartData.map((value, index) => {
    if (index <= currentFiscalMonthIndex) {
      return value; // Show actual data for current and past months
    } else {
      return null; // Hide future months
    }
  });

  console.log(`Scope2 Current fiscal month index: ${currentFiscalMonthIndex} (showing data up to ${fiscalMonths[currentFiscalMonthIndex]} - ${currentFYMonthKeys[currentFiscalMonthIndex]})`);

  // Filter state for months
  const [fromMonth, setFromMonth] = useState(monthlyCategories[0]);
  const [toMonth, setToMonth] = useState(monthlyCategories[monthlyCategories.length - 1]);

  // Helper to get filtered indices
  const fromIdx = monthlyCategories.indexOf(fromMonth);
  const toIdx = monthlyCategories.indexOf(toMonth);
  const filteredMonthlyCategories = monthlyCategories.slice(fromIdx, toIdx + 1);

  // Filter FY 24 data (full year available - keep all months)
  const filteredFY24Data = fy24Data.slice(fromIdx, toIdx + 1);

  // Filter FY 25 data (only up to current month, then apply user filter)
  const maxToIdx = Math.min(toIdx, Math.max(0, currentFiscalMonthIndex));
  const filteredFY25Data = fy25Data.slice(fromIdx, maxToIdx + 1);

  // Chart categories include total column + all filtered months (show all months for FY25)
  const finalScope2ChartCategories = [totalCategory[0], ...filteredMonthlyCategories];

  // Create dynamic color zones for current year line based on comparison with previous year
  const createDynamicZones = () => {
    const zones = [];
    const currentYearData = [null, ...filteredFY25Data]; // null for total column position
    const previousYearData = [null, ...filteredFY24Data.slice(0, filteredFY25Data.length)]; // match length

    for (let i = 1; i < currentYearData.length; i++) { // Start from 1 to skip null
      const currentValue = currentYearData[i];
      const previousValue = previousYearData[i];

      if (currentValue !== null && previousValue !== null) {
        const isImprovement = currentValue <= previousValue; // Lower or equal emissions = better
        zones.push({
          value: i,
          color: isImprovement ? '#22c55e' : '#ef4444', // Green if improved, Red if worse
        });
      }
    }

    // Add final zone to cover the rest
    if (zones.length > 0 && filteredFY25Data.length > 0 && filteredFY24Data.length > 0) {
      const lastCurrentValue = filteredFY25Data[filteredFY25Data.length - 1];
      const lastPreviousValue = filteredFY24Data[filteredFY25Data.length - 1];
      const lastComparison = lastCurrentValue <= lastPreviousValue;
      zones.push({
        value: currentYearData.length,
        color: lastComparison ? '#22c55e' : '#ef4444',
      });
    }

    return zones;
  };

  const dynamicZones = createDynamicZones();

  const options: Highcharts.Options = {
    chart: {
      backgroundColor: "#ffffff",
      style: {
        fontFamily:
          '"Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      height: 450,
      spacingTop: 30,
      spacingBottom: 80,
      spacingLeft: 60,
      spacingRight: 40,
      plotBorderWidth: 0,
      borderRadius: 8,
    },
    title: {
      text: undefined,
    },
    xAxis: {
      categories: finalScope2ChartCategories,
      title: {
        text: "",
        style: {
          fontSize: "13px",
          color: "#6B7280",
          fontWeight: "500",
        },
        margin: 20,
      },
      labels: {
        style: {
          fontSize: "12px",
          color: "#6B7280",
          fontWeight: "400",
        },
        rotation: 0,
        y: 25,
        useHTML: true,
        formatter: function () {
          // Show 'FY 24 Total' under the first category
          if (this.pos === 0) {
            return (
              '<span style="display: flex; flex-direction: column; align-items: center;">'
              + '<span style="font-weight: 600; color: #374151; margin-bottom: 2px;">' + this.value + '</span>'
              + '<span style="font-weight: 600; color: #374151;">Reference Year</span>'
              + '</span>'
            );
          }
          // Just return the month name directly for other categories
          return String(this.value);
        },
      },
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
    },
    yAxis: {
      min: 0,
      max: Math.ceil(Math.max(fy23Total, ...fy24Data, ...fy25Data) / 500) * 500,
      tickInterval: 500,
      title: {
        text: "tCO₂e (tons of CO₂ equivalent)",
        style: {
          fontSize: "12px",
          color: "#374151",
          fontWeight: "500",
        },
        margin: 30,
        rotation: 270,
      },
      labels: {
        style: {
          fontSize: "12px",
          color: "#6B7280",
          fontWeight: "400",
        },
        x: -15,
        formatter: function () {
          return this.value.toString();
        },
      },
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "bottom",
      itemStyle: {
        fontSize: "12px",
        color: "#374151",
        fontWeight: "500",
      },
      itemHoverStyle: {
        color: "#111827",
      },
      itemMarginTop: 5,
      itemMarginBottom: 5,
      symbolHeight: 12,
      symbolWidth: 20,
      symbolRadius: 2,
      y: 15,
      borderWidth: 0,
    },
    tooltip: {
      shared: true,
      backgroundColor: "#FFFFFF",
      borderColor: "#E5E7EB",
      borderRadius: 8,
      borderWidth: 1,
      shadow: {
        color: "rgba(0, 0, 0, 0.1)",
        offsetX: 0,
        offsetY: 2,
        opacity: 0.1,
        width: 4,
      },
      style: {
        fontSize: "12px",
        color: "#374151",
        fontWeight: "400",
      },
      useHTML: true,
      padding: 12,
      formatter: function () {
        console.log(this)
        let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
        this.points.forEach(function (point) {
          s += `<div style="font-weight: 600; color: #111827; margin-bottom: 4px;">${point.series.name}: ${Number(point.y).toFixed(2)} tCO₂e</div>`;
        });
        return s;
      },
    },
    plotOptions: {
      column: {
        borderRadius: 3,
        pointPadding: 0.15,
        groupPadding: 0.1,
        borderWidth: 0,
        pointWidth: 40,
        states: {
          hover: {
            brightness: 0.05,
            borderColor: "#374151",
            borderWidth: 1,
          },
        },
      },
      line: {
        marker: {
          enabled: true,
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
      },
    },
    series: [
      {
        type: "column",
        name: `${fy23Label} Total Actual`,
        data: [
          { y: fy23Total, color: "#9CA3AF" },
          ...Array(filteredMonthlyCategories.length).fill(null),
        ],
        showInLegend: false,
        zIndex: 1,
        borderRadius: 3,
      },
      {
        type: "line",
        name: fy24Label,
        data: [null, ...fy24ChartData],
        color: "#9CA3AF", // grey for previous year
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 2,
      },
      {
        type: "line",
        name: fy25Label,
        data: [null, ...fy25ChartData, ...Array(filteredMonthlyCategories.length - fy25ChartData.length).fill(null)],
        color: "#9CA3AF", // default grey, will be overridden by zones
        zones: dynamicZones,
        zoneAxis: 'x',
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
          fillColor: null, // use line color
        },
        lineWidth: 3,
        zIndex: 2,
      },
    ],
    credits: { enabled: false },
  };

  // Handle loading state
  if (loading) {
    const loadingTitle = `Scope 2 Emissions - Norton (FY ${(year - 1).toString().slice(-2)} vs FY ${year.toString().slice(-2)} Comparison)`;
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {loadingTitle}
              </h3>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white flex items-center justify-center h-96">
          <div className="text-center">
            <div className="relative mx-auto mb-4 w-12 h-12">
              <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
              <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
            </div>
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Handle no data state - check both raw data and filtered data
  if (!data || data.length === 0) {
    const noDataTitle = `Scope 2 Emissions - Norton (FY ${(year - 1).toString().slice(-2)} vs FY ${year.toString().slice(-2)} Comparison)`;
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {noDataTitle}
              </h3>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white flex items-center justify-center h-96">
          <div className="text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-gray-500 text-lg font-medium">No data to show</p>
            <p className="text-gray-400 text-sm mt-2">No emissions data available for the selected criteria</p>
          </div>
        </div>
      </div>
    );
  }

  // If we have raw data but no filtered data, show debug info
  if (!hasAnyData) {
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {chartTitle} - Debug Mode
              </h3>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white">
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-yellow-800">Data Available But No Matches Found</h4>
              <p className="text-yellow-700 mt-2">
                We have {data.length} data items, but none match the fiscal year criteria.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Raw Data Length:</strong> {data.length}</div>
              <div><strong>Target Year (FY{year.toString().slice(-2)}):</strong> {year}</div>
              <div><strong>FY Start Month:</strong> {fymonth}</div>
              <div><strong>FY25 Matches:</strong> {fy25DataFiltered.length}</div>
              <div><strong>FY24 Matches:</strong> {fy24DataFiltered.length}</div>
              <div><strong>FY23 Matches:</strong> {fy23DataFiltered.length}</div>
            </div>

            {data.length > 0 && (
              <div className="mt-4">
                <h5 className="font-semibold">Sample Data Item:</h5>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(data[0], null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          {/* Chart Title and Details - now on the left */}
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {chartTitle}
            </h3>
          </div>


        </div>
      </div>

      {/* Filter Controls and Results Row */}
      <div className="flex flex-row justify-between items-end px-6 pt-4 gap-8">
        <div className="flex flex-row items-center gap-6" style={{ display: 'none' }}>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">From</span>
            <select
              className="border rounded px-2 py-1"
              value={fromMonth}
              onChange={e => {
                const val = e.target.value;
                setFromMonth(val);
                if (monthlyCategories.indexOf(val) > monthlyCategories.indexOf(toMonth)) {
                  setToMonth(val);
                }
              }}
            >
              {monthlyCategories.map((month, idx) => (
                <option key={month} value={month} disabled={idx > monthlyCategories.indexOf(toMonth)}>
                  {month}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">To</span>
            <select
              className="border rounded px-2 py-1"
              value={toMonth}
              onChange={e => {
                const val = e.target.value;
                setToMonth(val);
                if (monthlyCategories.indexOf(val) < monthlyCategories.indexOf(fromMonth)) {
                  setFromMonth(val);
                }
              }}
            >
              {monthlyCategories.map((month, idx) => (
                <option key={month} value={month} disabled={idx < monthlyCategories.indexOf(fromMonth)}>
                  {month}
                </option>
              ))}
            </select>
          </div>
        </div>
        {/* Debug Information Row */}
        <div className="flex flex-row gap-6 justify-content-end mb-4">
          <div className="bg-yellow-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-yellow-900 mr-2">Debug - FY25 Total from Periods:</span> {fy25TotalFromProcessed.toFixed(3)} tCO₂e
          </div>
          <div className="bg-yellow-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-yellow-900 mr-2">Debug - FY25 Sum of Months:</span> {fy25ChartData.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
        </div>

        {/* Filtered Results Row */}
        <div className="flex flex-row gap-6 justify-content-end">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-gray-900 mr-2">{fy24Label} Total (YTD):</span> {fy24ChartData.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className="bg-blue-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-blue-900 mr-2">{fy25Label} Total (YTD):</span> {fy25ChartData.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className={`rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center ${(fy25ChartData.reduce((a: number, b: number) => a + b, 0) - fy24ChartData.reduce((a: number, b: number) => a + b, 0)) <= 0
              ? 'bg-green-100'
              : 'bg-red-100'
            }`}>
            <span className={`font-semibold mr-2 ${(fy25ChartData.reduce((a: number, b: number) => a + b, 0) - fy24ChartData.reduce((a: number, b: number) => a + b, 0)) < 0
                ? 'text-green-900'
                : 'text-red-900'
              }`}>
              {(fy25ChartData.reduce((a: number, b: number) => a + b, 0) - fy24ChartData.reduce((a: number, b: number) => a + b, 0)) < 0 ? 'Improvement:' : 'Increase:'}
            </span>
            {Math.abs(fy25ChartData.reduce((a: number, b: number) => a + b, 0) - fy24ChartData.reduce((a: number, b: number) => a + b, 0)).toFixed(3)} tCO₂e
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="p-6 bg-white">
        <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500 font-medium">
          Green Line: Within baseline target  | Red Line: Above baseline target
        </p>
      </div>
    </div>
  );
};

// Scope 1 Emissions - Hosur Chart
export const Scope1EmissionsHosurChart: React.FC = () => {
  const chartRef = useRef<any>(null);

  // Categories for x-axis - total column + monthly categories
  const totalCategory = ["FY 24 Total"];
  const monthlyCategories = [
    "Apr", "May", "Jun", "Jul", "Aug", "Sep",
    "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
  ];

  // Get current month to limit FY 26 data (assuming we're in FY 26)
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth(); // 0-11
  const currentFiscalMonth = currentMonth >= 3 ? currentMonth - 3 : currentMonth + 9; // Apr=0, May=1, ..., Mar=11

  // Data for three fiscal years
  const fy24Total = 13616.004; // FY 24 total (Current FY - 2)

  // FY 25 data (Previous FY - grey line) - empty array as requested
  const fy25Data = [1142.655, 1132.91, 1007.655, 1023.118, 969.761, 1135.731, 981.65, 1070.685, 963.164, 1193.442, 1315.998, 1048.848]


  // FY 26 data (Current FY - dynamic color line) - keep existing data
  const fy26FullData = [

  ];

  // Limit FY 26 data to current month
  const fy26Data = fy26FullData.slice(0, currentFiscalMonth + 1);

  // Filter data based on current month
  const filteredMonthlyCategories = monthlyCategories;
  const filteredFY25Data = fy25Data;
  const filteredFY26Data = fy26Data;

  // Chart categories: total + monthly
  const chartCategories = [...totalCategory, ...filteredMonthlyCategories];
  // Create dynamic zones for FY 26 line based on comparison with FY 25
  const dynamicZones = [];
  for (let i = 1; i <= filteredFY26Data.length; i++) {
    const currentValue = filteredFY26Data[i - 1];
    const previousValue = filteredFY25Data[i - 1];

    if (currentValue !== null && previousValue !== null) {
      const isImprovement = currentValue <= previousValue; // Lower or equal emissions = better
      dynamicZones.push({
        value: i,
        color: isImprovement ? '#22c55e' : '#ef4444', // Green if improved or equal, Red if worse
      });
    }
  }

  // Add final zone for remaining line
  if (filteredFY26Data.length > 0) {
    const lastCurrentValue = filteredFY26Data[filteredFY26Data.length - 1];
    const lastPreviousValue = filteredFY25Data[filteredFY26Data.length - 1];
    if (lastCurrentValue !== null && lastPreviousValue !== null) {
      const lastComparison = lastCurrentValue <= lastPreviousValue;
      dynamicZones.push({
        value: filteredFY26Data.length + 1,
        color: lastComparison ? '#22c55e' : '#ef4444',
      });
    }
  }
  const options: Highcharts.Options = {
    chart: {
      backgroundColor: "#ffffff",
      style: {
        fontFamily:
          '"Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      height: 450,
      spacingTop: 30,
      spacingBottom: 80,
      spacingLeft: 60,
      spacingRight: 40,
      plotBorderWidth: 0,
      borderRadius: 8,
    },
    title: { text: undefined },
    xAxis: {
      categories: chartCategories,
      title: { text: "", style: { fontSize: "13px", color: "#6B7280", fontWeight: "500" }, margin: 20 },
      labels: {
        style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
        rotation: 0,
        y: 25,
        useHTML: true,
        formatter: function () {
          if (this.pos === 0) {
            return (
              '<span style="display: flex; flex-direction: column; align-items: center;">'
              + '<span style="font-weight: 600; color: #374151; margin-bottom: 2px;">' + this.value + '</span>'
              + '<span style="font-weight: 600; color: #374151;">Last Year\'s Actual</span>'
              + '</span>'
            );
          }
          // Extract month name from "Apr-24" format to show just "Apr"
          const monthYear = String(this.value);
          const monthName = monthYear.split('-')[0];
          return monthName;
        },
      },
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
    },
    yAxis: {
      min: 0,
      max: Math.ceil(Math.max(fy24Total, ...fy26FullData) / 1000) * 1000,
      tickInterval: 2000,
      title: {
        text: "tCO₂e (tons of CO₂ equivalent)",
        style: { fontSize: "12px", color: "#374151", fontWeight: "500" },
        margin: 30,
        rotation: 270,
      },
      labels: {
        style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
        x: -15,
        formatter: function () { return this.value.toString(); },
      },
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "bottom",
      itemStyle: { fontSize: "12px", color: "#374151", fontWeight: "500" },
      itemHoverStyle: { color: "#111827" },
      itemMarginTop: 5,
      itemMarginBottom: 5,
      symbolHeight: 12,
      symbolWidth: 20,
      symbolRadius: 2,
      y: 15,
      borderWidth: 0,
    },
    tooltip: {
      shared: true,
      backgroundColor: "#FFFFFF",
      borderColor: "#E5E7EB",
      borderRadius: 8,
      borderWidth: 1,
      shadow: {
        color: "rgba(18, 15, 15, 0.1)",
        offsetX: 0,
        offsetY: 2,
        opacity: 0.1,
        width: 4,
      },
      style: { fontSize: "12px", color: "#374151", fontWeight: "400" },
      useHTML: true,
      padding: 12,
      formatter: function () {
        console.log(this)
        let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
        this.points.forEach(function (point) {
          const color = point.series.color;
          s += `<div style="margin-bottom: 4px;">
                  <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                  <span style="font-weight: 600; color: #111827;">${Number(point.y).toFixed(2)} tCO₂e</span>
                </div>`;
        });
        return s;
      },
    },
    plotOptions: {
      column: {
        borderRadius: 3,
        pointPadding: 0.15,
        groupPadding: 0.1,
        borderWidth: 0,
        pointWidth: 40,
        states: {
          hover: {
            brightness: 0.05,
            borderColor: "#374151",
            borderWidth: 1,
          },
        },
      },
      line: {
        marker: {
          enabled: true,
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
      },
    },
    series: [
      {
        type: "column",
        name: "FY 24 Total Actual",
        data: [
          { y: fy24Total, color: "#9CA3AF" },
          ...Array(filteredMonthlyCategories.length).fill(null),
        ],
        showInLegend: false,
        zIndex: 1,
        borderRadius: 3,
      },
      {
        type: "line",
        name: fy25Label,
        data: [null, ...filteredFY25Data],
        color: "#9CA3AF", // grey for previous year
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 2,
      },
      {
        type: "line",
        name: fy26Label,
        data: [null, ...filteredFY26Data, ...Array(filteredMonthlyCategories.length - filteredFY26Data.length).fill(null)],
        color: "#9CA3AF", // default color, will be overridden by zones
        zones: dynamicZones,
        zoneAxis: 'x',
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 3,
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
      <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Scope 1 Emissions - Hosur (FY 25 vs FY 26 Comparison)
            </h3>
          </div>


        </div>
      </div>
      <div className="flex flex-row justify-between items-end px-6 pt-4 gap-8">
        <div className="flex flex-row items-center gap-6" style={{ display: 'none' }}>
        </div>
        <div className="flex flex-row gap-6 justify-content-end">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-gray-900 mr-2">{fy25Label} Total (YTD):</span> {filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className="bg-blue-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-blue-900 mr-2">{fy26Label} Total (YTD):</span> {filteredFY26Data.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className={`rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0
              ? 'bg-green-100'
              : 'bg-red-100'
            }`}>
            <span className={`font-semibold mr-2 ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0
                ? 'text-green-900'
                : 'text-red-900'
              }`}>
              {(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0 ? 'Improvement:' : 'Increase:'}
            </span>
            {Math.abs(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)).toFixed(3)} tCO₂e
          </div>
        </div>
      </div>
      <div className="p-6 bg-white">
        <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
      </div>
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500 font-medium">
          Green Line: Within baseline target  | Red Line: Above baseline target
        </p>
      </div>
    </div>
  );
};

// Scope 2 Emissions - Hosur Chart
export const Scope2EmissionsHosurChart: React.FC = () => {
  const chartRef = useRef<any>(null);

  // Categories for x-axis - total column + monthly categories
  const totalCategory = ["FY 24 Total"];
  const monthlyCategories = [
    "Apr", "May", "Jun", "Jul", "Aug", "Sep",
    "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"
  ];

  // Get current month to limit FY 26 data (assuming we're in FY 26)
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth(); // 0-11
  const currentFiscalMonth = currentMonth >= 3 ? currentMonth - 3 : currentMonth + 9; // Apr=0, May=1, ..., Mar=11

  // Data for three fiscal years
  const fy24Total = 2635.872; // FY 24 total (Current FY - 2)

  // FY 25 data (Previous FY - grey line) - empty array as requested
  const fy25Data = [557.821, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 299.23];

  // FY 26 data (Current FY - dynamic color line) - keep existing data
  const fy26FullData = [

  ];

  // Limit FY 26 data to current month
  const fy26Data = fy26FullData.slice(0, currentFiscalMonth + 1);

  // Filter data based on current month
  const filteredMonthlyCategories = monthlyCategories;
  const filteredFY25Data = fy25Data;
  const filteredFY26Data = fy26Data;

  // Chart categories: total + monthly
  const chartCategories = [...totalCategory, ...filteredMonthlyCategories];

  // Create dynamic zones for FY 26 line based on comparison with FY 25
  const dynamicZones = [];
  for (let i = 1; i <= filteredFY26Data.length; i++) {
    const currentValue = filteredFY26Data[i - 1];
    const previousValue = filteredFY25Data[i - 1];

    if (currentValue !== null && previousValue !== null) {
      const isImprovement = currentValue <= previousValue; // Lower or equal emissions = better
      dynamicZones.push({
        value: i,
        color: isImprovement ? '#22c55e' : '#ef4444', // Green if improved or equal, Red if worse
      });
    }
  }

  // Add final zone for remaining line
  if (filteredFY26Data.length > 0) {
    const lastCurrentValue = filteredFY26Data[filteredFY26Data.length - 1];
    const lastPreviousValue = filteredFY25Data[filteredFY26Data.length - 1];
    if (lastCurrentValue !== null && lastPreviousValue !== null) {
      const lastComparison = lastCurrentValue <= lastPreviousValue;
      dynamicZones.push({
        value: filteredFY26Data.length + 1,
        color: lastComparison ? '#22c55e' : '#ef4444',
      });
    }
  }

  const options: Highcharts.Options = {
    chart: {
      backgroundColor: "#ffffff",
      style: {
        fontFamily:
          '"Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      height: 450,
      spacingTop: 30,
      spacingBottom: 80,
      spacingLeft: 60,
      spacingRight: 40,
      plotBorderWidth: 0,
      borderRadius: 8,
    },
    title: { text: undefined },
    xAxis: {
      categories: chartCategories,
      title: { text: "", style: { fontSize: "13px", color: "#6B7280", fontWeight: "500" }, margin: 20 },
      labels: {
        style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
        rotation: 0,
        y: 25,
        useHTML: true,
        formatter: function () {
          if (this.pos === 0) {
            return (
              '<span style="display: flex; flex-direction: column; align-items: center;">'
              + '<span style="font-weight: 600; color: #374151; margin-bottom: 2px;">' + this.value + '</span>'
              + '<span style="font-weight: 600; color: #374151;">Last Year\'s Actual</span>'
              + '</span>'
            );
          }
          // Extract month name from "Apr-24" format to show just "Apr"
          const monthYear = String(this.value);
          const monthName = monthYear.split('-')[0];
          return monthName;
        },
      },
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
    },
    yAxis: {
      min: 0,
      max: Math.ceil(Math.max(fy24Total, ...fy26FullData) / 500) * 500,
      tickInterval: 500,
      title: {
        text: "tCO₂e (tons of CO₂ equivalent)",
        style: { fontSize: "12px", color: "#374151", fontWeight: "500" },
        margin: 30,
        rotation: 270,
      },
      labels: {
        style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
        x: -15,
        formatter: function () { return this.value.toString(); },
      },
      gridLineWidth: 1,
      gridLineColor: "#F3F4F6",
      lineWidth: 1,
      lineColor: "#E5E7EB",
      tickWidth: 1,
      tickColor: "#E5E7EB",
    },
    legend: {
      enabled: true,
      layout: "horizontal",
      align: "center",
      verticalAlign: "bottom",
      itemStyle: { fontSize: "12px", color: "#374151", fontWeight: "500" },
      itemHoverStyle: { color: "#111827" },
      itemMarginTop: 5,
      itemMarginBottom: 5,
      symbolHeight: 12,
      symbolWidth: 20,
      symbolRadius: 2,
      y: 15,
      borderWidth: 0,
    },
    tooltip: {
      shared: true,
      backgroundColor: "#FFFFFF",
      borderColor: "#E5E7EB",
      borderRadius: 8,
      borderWidth: 1,
      shadow: {
        color: "rgba(0, 0, 0, 0.1)",
        offsetX: 0,
        offsetY: 2,
        opacity: 0.1,
        width: 4,
      },
      style: { fontSize: "12px", color: "#374151", fontWeight: "400" },
      useHTML: true,
      padding: 12,
      formatter: function () {
        console.log(this)
        let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
        this.points.forEach(function (point) {
          const color = point.series.color;
          s += `<div style="margin-bottom: 4px;">
                  <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                  <span style="font-weight: 600; color: #111827;">${Number(point.y).toFixed(2)} tCO₂e</span>
                </div>`;
        });
        return s;
      },
    },
    plotOptions: {
      column: {
        borderRadius: 3,
        pointPadding: 0.15,
        groupPadding: 0.1,
        borderWidth: 0,
        pointWidth: 40,
        states: {
          hover: {
            brightness: 0.05,
            borderColor: "#374151",
            borderWidth: 1,
          },
        },
      },
      line: {
        marker: {
          enabled: true,
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
      },
    },
    series: [
      {
        type: "column",
        name: `${fy24Label} Total Actual`,
        data: [
          { y: fy24Total, color: "#9CA3AF" },
          ...Array(filteredMonthlyCategories.length).fill(null),
        ],
        showInLegend: false,
        zIndex: 1,
        borderRadius: 3,
      },
      {
        type: "line",
        name: fy25Label,
        data: [null, ...filteredFY25Data],
        color: "#9CA3AF", // grey for previous year
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 2,
      },
      {
        type: "line",
        name: fy26Label,
        data: [null, ...filteredFY26Data, ...Array(filteredMonthlyCategories.length - filteredFY26Data.length).fill(null)],
        color: "#9CA3AF", // default color, will be overridden by zones
        zones: dynamicZones,
        zoneAxis: 'x',
        marker: {
          symbol: "circle",
          radius: 4,
          lineWidth: 2,
          lineColor: "#FFFFFF",
        },
        lineWidth: 3,
        zIndex: 3,
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden mt-12">
      <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Scope 2 Emissions - Hosur (FY 25 vs FY 26 Comparison)
            </h3>
          </div>


        </div>
      </div>
      <div className="flex flex-row justify-between items-end px-6 pt-4 gap-8">
        <div className="flex flex-row items-center gap-6" style={{ display: 'none' }}>
        </div>
        <div className="flex flex-row gap-6 justify-content-end">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-gray-900 mr-2">{fy25Label} Total (YTD) :</span> {filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className="bg-blue-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
            <span className="font-semibold text-blue-900 mr-2">{fy26Label} Total (YTD):</span> {filteredFY26Data.reduce((a: number, b: number) => a + b, 0).toFixed(3)} tCO₂e
          </div>
          <div className={`rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0
              ? 'bg-green-100'
              : 'bg-red-100'
            }`}>
            <span className={`font-semibold mr-2 ${(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0
                ? 'text-green-900'
                : 'text-red-900'
              }`}>
              {(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)) < 0 ? 'Improvement:' : 'Increase:'}
            </span>
            {Math.abs(filteredFY26Data.reduce((a: number, b: number) => a + b, 0) - filteredFY25Data.slice(0, filteredFY26Data.length).reduce((a: number, b: number) => a + b, 0)).toFixed(3)} tCO₂e
          </div>
        </div>
      </div>
      <div className="p-6 bg-white">
        <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
      </div>
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500 font-medium">
          Green Line: Within baseline target  | Red Line: Above baseline target
        </p>
      </div>
    </div>
  );
};
