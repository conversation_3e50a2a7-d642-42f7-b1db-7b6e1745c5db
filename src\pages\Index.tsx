import { useState, useEffect } from 'react';
import EnergyPieChart from '@/components/charts/EnergyPieChart';
import EnergyLineChart from '@/components/charts/EnergyLineChart';
import WaterWithdrawalChart from '@/components/charts/WaterWithdrawalChart';
import WaterConsumptionChart from '@/components/charts/WaterConsumptionChart';
import WasteGenerationChart from '@/components/charts/WasteGenerationChart';
import NonHazardousWasteCategoryChart from '@/components/charts/NonHazardousWasteCategoryChart';
import HazardousWasteCategoryChart from '@/components/charts/HazardousWasteCategoryChart';
import NonHazardousWasteDisposalChart from '@/components/charts/NonHazardousWasteDisposalChart';
import HazardousWasteDisposalChart from '@/components/charts/HazardousWasteDisposalChart';
import MonthFilter from '@/components/ui/MonthFilter';
import wasteData from '@/data/wasteData.json';
import axios from 'axios'
import { EnergyEmissionsRatingsChart, Scope2EmissionsNortonChart } from '@/components/charts/EnergyEmissionsRatingsChart';
import { useLocation } from "react-router-dom";
import crypto from "crypto-js"
import { filterDataByTierAndLocationByLevel } from '@/hooks/helper';


const SECRET_KEY = "e!sq6esgdash1";
const Index = () => {
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const months = wasteData.nonHazardousCategory.months;
  const [locationData, setLocationData] = useState<any[]>([]);
  const [access, setAccess] = useState(false)
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isIndicatorLoading, setIsIndicatorLoading] = useState<boolean>(false);
  const [supplierData, setSupplierData] = useState<any[]>([]);
  const [clientData, setClientData] = useState({ login_data: null, admin_data: null });
  const [clientId, setClientId] = useState<string>("289");
  const [clientConfig, setClientConfig] = useState({ features: { drilldown: true, locationFilter: true } });
  const [indicatorData, setIndicatorData] = useState([]);

  const params = useLocation()
  const [param, setParam] = useState(params.search?.slice(1))
  function decryptNumber(encrypted) {
    var bytes = crypto.AES.decrypt(encrypted, SECRET_KEY);
    var originalText = bytes.toString(crypto.enc.Utf8);
    return originalText
  }
  function encryptNumber(number, secret) {
    return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
  }
  useEffect(() => {


      setIsLoading(true);

      const loadAllData = async () => {
        try {

          const locationData = await fetchLocationData("all");
          setLocationData(locationData);
     

        } catch (err) {
          console.error("Error loading initial data:", err);
        } finally {
          setIsLoading(false);
        }
      };

      loadAllData();
    
  }, []);

  useEffect(() => {
    // 339
    console.log(encryptNumber(JSON.stringify({ userId: 374, adminId: 291, isUKUser: true }), SECRET_KEY))
    if (param) {
      const { userId, adminId, isUKUser } = JSON.parse(decryptNumber(param))
      setAccess((userId === 339 || userId === 374) ? true : isUKUser ? true : false)
      setClientData({ login_data: userId, admin_data: adminId })
    }

  }, [param])
    useEffect(() => {
    const  callIndicatorAPI = async() => {
if(locationData.length){
       setIsIndicatorLoading(true);
       try {
         const indicatorData = await fetchIndicatorData('2021');
         console.log(indicatorData)
         setIndicatorData(indicatorData)
       } catch (error) {
         console.error('Error fetching indicator data:', error);
         setIndicatorData([]);
       } finally {
         setIsIndicatorLoading(false);
       }
}
      }
callIndicatorAPI()

  }, [locationData])
  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        `https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D`
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };
  const fetchIndicatorData = async (year: string) => {
    try {
      const response = await axios.post(
        `https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator`, { year: { startMonth: "Apr-2023", endMonth: "Mar-2026" }, indicatorId: [162, 163,202,481] }
      );
console.log(locationData)

      return filterDataByTierAndLocationByLevel(response?.data, locationData, 106, 134,0)
    } catch (err) {
      console.warn("error:", err);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-dashboard p-6">
      {access ? <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-8">
          {/* <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-chart-renewable to-chart-water-surface bg-clip-text text-transparent">
            Sustainability Dashboard
          </h1> */}
          <p className="text-xl text-muted-foreground">Environmental Impact & Resource Management Analytics</p>
        </div>
        <div className="mt-10">
          <EnergyEmissionsRatingsChart
            region="Global"
            data={indicatorData.filter((item: any) => item.indicatorId === 162)}
            year={2026}  // Max year (FY26)
            fymonth={4}  // April start (Apr 2025 to Mar 2026 for FY26)
            loading={isIndicatorLoading}
          />
          <Scope2EmissionsNortonChart
            data={indicatorData.filter((item: any) => item.indicatorId === 163)}
            year={2026}  // Max year (FY26)
            fymonth={4}  // April start
            loading={isIndicatorLoading}
          />
        </div>

        {/* Energy Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Energy Analysis
          </h2>

          {/* Pie Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EnergyPieChart
              data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2025}  // FY25
              fymonth={4}  // April start
              loading={isIndicatorLoading}
              fiscalYear="fy25" // For backward compatibility
            />
            <EnergyPieChart
              data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2025}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading}
              fiscalYear="fy26" // For backward compatibility
            />
          </div>

          {/* Line Chart */}
          <EnergyLineChart  data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} />
        </section>

        {/* Water Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Water Consumption Analysis
          </h2>

          {/* Water Withdrawal Chart */}
          <WaterWithdrawalChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading}  />

          {/* Water Consumption by Type - Three Charts in Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="surface"
              type="surfaceWater"
              title="Surface Water Consumption"
              color="hsl(var(--chart-water-surface))"
            />
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="ground"
              type="groundWater"
              title="Ground Water Consumption"
              color="hsl(var(--chart-water-ground))"
            />
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="third"
              type="thirdPartyWater"
              title="Third Party Water Consumption"
              color="hsl(var(--chart-water-third))"
            />
          </div>
        </section>

        {/* Waste Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Waste Generation - Norton
          </h2>

          <WasteGenerationChart />

          {/* Month Filter */}
          <div className="flex justify-center">
            <MonthFilter
              selectedMonth={selectedMonth}
              onMonthChange={setSelectedMonth}
              months={months}
            />
          </div>

          {/* Waste Category Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NonHazardousWasteCategoryChart selectedMonth={selectedMonth} />
            <HazardousWasteCategoryChart selectedMonth={selectedMonth} />
          </div>

          {/* Waste Disposal Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NonHazardousWasteDisposalChart selectedMonth={selectedMonth} />
            <HazardousWasteDisposalChart selectedMonth={selectedMonth} />
          </div>
        </section>
      </div> :
        <div>You don't have neccessary permission to access this page </div>
      }
    </div>
  );
};

export default Index;
