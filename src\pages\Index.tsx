import { useState, useEffect } from 'react';
import EnergyPieChart from '@/components/charts/EnergyPieChart';
import EnergyLineChart from '@/components/charts/EnergyLineChart';
import WaterWithdrawalChart from '@/components/charts/WaterWithdrawalChart';
import WaterConsumptionChart from '@/components/charts/WaterConsumptionChart';
import WasteGenerationChart from '@/components/charts/WasteGenerationChart';
import NonHazardousWasteCategoryChart from '@/components/charts/NonHazardousWasteCategoryChart';
import HazardousWasteCategoryChart from '@/components/charts/HazardousWasteCategoryChart';
import NonHazardousWasteDisposalChart from '@/components/charts/NonHazardousWasteDisposalChart';
import HazardousWasteDisposalChart from '@/components/charts/HazardousWasteDisposalChart';
import YearFilter from '@/components/filters/YearFilter';
import axios from 'axios'
import { EnergyEmissionsRatingsChart, Scope2EmissionsNorton<PERSON>hart } from '@/components/charts/EnergyEmissionsRatingsChart';
import { useLocation } from "react-router-dom";
import crypto from "crypto-js"
import { filterDataByTierAndLocationByLevel } from '@/hooks/helper';


const SECRET_KEY = "e!sq6esgdash1";
const Index = () => {
  const [selectedYear, setSelectedYear] = useState<number>(2025); // Default to FY25
  const [locationData, setLocationData] = useState<any[]>([]);
  const [access, setAccess] = useState(false)
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isIndicatorLoading, setIsIndicatorLoading] = useState<boolean>(false);
  const [supplierData, setSupplierData] = useState<any[]>([]);
  const [clientData, setClientData] = useState({ login_data: null, admin_data: null });
  const [clientId, setClientId] = useState<string>("289");
  const [clientConfig, setClientConfig] = useState({ features: { drilldown: true, locationFilter: true } });
  const [indicatorData, setIndicatorData] = useState([]);
 const wasteIndicatorData = [
    // Hazardous Solid - indicatorId: "300"
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '1.16', value: '1.16', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0.5', value: '0.5', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '3.2', value: '3.2', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Hazardous Solid', actualTitle: 'Hazardous > Solid', disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },

    // Metal - indicatorId: "300" (same for all)
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0.68', value: '0.68', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '6.56', value: '6.56', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '1.6', value: '1.6', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Metal', actualTitle: 'Non Hazardous > Metal', disposalMethod: 'Recycle', computedValue: '0', value: '0', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },

    // Non Hazardous Sludge - indicatorId: "300" (same for all)
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '5', value: '5', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '5', value: '5', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '5', value: '5', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '3', value: '3', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '5', value: '5', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Sludge', actualTitle: 'Non Hazardous > Sludge', disposalMethod: 'Waste to energy', computedValue: '4', value: '4', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },

    // Non Hazardous Solid Mixed - indicatorId: "300" (same for all)
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.4', value: '2.4', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '3', value: '3', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.44', value: '2.44', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.7', value: '2.7', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '3', value: '3', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.4', value: '2.4', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.7', value: '2.7', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.7', value: '2.7', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '1.8', value: '1.8', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.87', value: '2.87', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.4', value: '2.4', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Mixed', actualTitle: 'Non Hazardous > Solid Mixed',disposalMethod: 'Refuse', computedValue: '2.4', value: '2.4', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },

    // Non Hazardous Solid Segregated - indicatorId: "300" (same for all)
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '4.848', value: '4.848', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.183', value: '3.183', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.72', value: '3.72', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.094', value: '3.094', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.095', value: '3.095', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '2.72', value: '2.72', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '1.824', value: '1.824', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.07', value: '3.07', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '1.303', value: '1.303', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.74', value: '3.74', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '1.6', value: '1.6', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Non Hazardous Solid Segregated', actualTitle: 'Non Hazardous > Solid Segregated',disposalMethod: 'Refuse', computedValue: '3.184', value: '3.184', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },

    // Plastic Waste Mixed - indicatorId: "300" (same for all)
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['04-2024'], reporting_period: 'Apr-2024', periodFrom: '04-2024', periodTo: '04-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['05-2024'], reporting_period: 'May-2024', periodFrom: '05-2024', periodTo: '05-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['06-2024'], reporting_period: 'Jun-2024', periodFrom: '06-2024', periodTo: '06-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['07-2024'], reporting_period: 'Jul-2024', periodFrom: '07-2024', periodTo: '07-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['08-2024'], reporting_period: 'Aug-2024', periodFrom: '08-2024', periodTo: '08-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['09-2024'], reporting_period: 'Sep-2024', periodFrom: '09-2024', periodTo: '09-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0.34', value: '0.34', rp: ['10-2024'], reporting_period: 'Oct-2024', periodFrom: '10-2024', periodTo: '10-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['11-2024'], reporting_period: 'Nov-2024', periodFrom: '11-2024', periodTo: '11-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['12-2024'], reporting_period: 'Dec-2024', periodFrom: '12-2024', periodTo: '12-2024', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['01-2025'], reporting_period: 'Jan-2025', periodFrom: '01-2025', periodTo: '01-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['02-2025'], reporting_period: 'Feb-2025', periodFrom: '02-2025', periodTo: '02-2025', unitOfMeasure: 'MT' },
    { indicatorId: "300", indicatorUnit: 'MT', title: 'Plastic Waste Mixed', actualTitle: 'Non Hazardous > Plastic Waste Mixed',disposalMethod: 'Waste to energy', computedValue: '0', value: '0', rp: ['03-2025'], reporting_period: 'Mar-2025', periodFrom: '03-2025', periodTo: '03-2025', unitOfMeasure: 'MT' },
  ];
  const params = useLocation()
  const [param, setParam] = useState(params.search?.slice(1))
  function decryptNumber(encrypted) {
    var bytes = crypto.AES.decrypt(encrypted, SECRET_KEY);
    var originalText = bytes.toString(crypto.enc.Utf8);
    return originalText
  }
  function encryptNumber(number, secret) {
    return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
  }
  useEffect(() => {


      setIsLoading(true);

      const loadAllData = async () => {
        try {

          const locationData = await fetchLocationData("all");
          setLocationData(locationData);
     

        } catch (err) {
          console.error("Error loading initial data:", err);
        } finally {
          setIsLoading(false);
        }
      };

      loadAllData();
    
  }, []);

  useEffect(() => {
    // 339
    console.log(encryptNumber(JSON.stringify({ userId: 374, adminId: 291, isUKUser: true }), SECRET_KEY))
    if (param) {
      const { userId, adminId, isUKUser } = JSON.parse(decryptNumber(param))
      setAccess((userId === 339 || userId === 374) ? true : isUKUser ? true : false)
      setClientData({ login_data: userId, admin_data: adminId })
    }

  }, [param])
    useEffect(() => {
    const  callIndicatorAPI = async() => {
if(locationData.length){
       setIsIndicatorLoading(true);
       try {
         const indicatorData = await fetchIndicatorData('2021');
         console.log(indicatorData)
         setIndicatorData(indicatorData)
       } catch (error) {
         console.error('Error fetching indicator data:', error);
         setIndicatorData([]);
       } finally {
         setIsIndicatorLoading(false);
       }
}
      }
callIndicatorAPI()

  }, [locationData])
  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        `https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D`
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };
  const fetchIndicatorData = async (year: string) => {
    try {
      const scope1 = await axios.post(
        `https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator`, { year: { startMonth: "Apr-2023", endMonth: "Mar-2026" }, indicatorId: [162] }
      );
      // const water = {data:[]},scope2 = {data:[]};
        const water = await axios.post(
        `https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator`, { year: { startMonth: "Apr-2023", endMonth: "Mar-2026" }, indicatorId: [202,481] }

      );
       const scope2 = await axios.post(
        `https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator`, { year: { startMonth: "Apr-2023", endMonth: "Mar-2026" }, indicatorId: [163] }
      );
console.log(locationData)

      return filterDataByTierAndLocationByLevel([...(scope1?.data||[]),...(scope2?.data||[]),...(water?.data||[])], locationData, 106, 134,0)
    } catch (err) {
      console.warn("error:", err);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-dashboard p-6">
      {access ? <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center mb-8">
          {/* <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-chart-renewable to-chart-water-surface bg-clip-text text-transparent">
            Sustainability Dashboard
          </h1> */}
          <p className="text-xl text-muted-foreground">Environmental Impact & Resource Management Analytics</p>
        </div>
        <div className="mt-10">
          <EnergyEmissionsRatingsChart
            region="Global"
            data={indicatorData.filter((item: any) => item.indicatorId === 162)}
            year={2026}  // Max year (FY26)
            fymonth={4}  // April start (Apr 2025 to Mar 2026 for FY26)
            loading={isIndicatorLoading}
          />
         
          <Scope2EmissionsNortonChart
            data={indicatorData.filter((item: any) => item.indicatorId === 163)}
            year={2026}  // FY25 (since all data belongs to FY25)
            fymonth={4}  // April start
            loading={isIndicatorLoading}
          />
        </div>

        {/* Energy Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Energy Analysis
          </h2>

          {/* Pie Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EnergyPieChart
              data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2025}  // FY25
              fymonth={4}  // April start
              loading={isIndicatorLoading}
            />
            <EnergyPieChart
              data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading}
            />
          </div>

          {/* Line Chart */}
          <EnergyLineChart  data={indicatorData.filter((item: any) => item.indicatorId === 481)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} />
        </section>

        {/* Water Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Water Consumption Analysis
          </h2>

          {/* Water Withdrawal Chart */}
          <WaterWithdrawalChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading}  />

          {/* Water Consumption by Type - Three Charts in Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="surface"
              type="surfaceWater"
              title="Surface Water Consumption"
              color="hsl(var(--chart-water-surface))"
            />
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="ground"
              type="groundWater"
              title="Ground Water Consumption"
              color="hsl(var(--chart-water-ground))"
            />
            <WaterConsumptionChart data={indicatorData.filter((item: any) => item.indicatorId === 202 && item.dcfId === 246)}
              year={2026}  // FY26
              fymonth={4}  // April start
              loading={isIndicatorLoading} match="third"
              type="thirdPartyWater"
              title="Third Party Water Consumption"
              color="hsl(var(--chart-water-third))"
            />
          </div>
        </section>

        {/* Waste Section */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground border-b border-border pb-2">
            Waste Generation - Norton
          </h2>

          <WasteGenerationChart
            data={wasteIndicatorData}
            year={2026}  // FY26
            fymonth={4}  // April start
            loading={false}
          />

          {/* Year Filter */}
          <div className="flex justify-center">
            <YearFilter
              selectedYear={selectedYear}
              onYearChange={setSelectedYear}
              years={[2024, 2025, 2026]}
            />
          </div>

          {/* Waste Category Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NonHazardousWasteCategoryChart
              data={wasteIndicatorData}
              year={selectedYear}
              fymonth={4}
              loading={false}
            />
            <HazardousWasteCategoryChart
              data={wasteIndicatorData}
              year={selectedYear}
              fymonth={4}
              loading={false}
            />
          </div>

          {/* Waste Disposal Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NonHazardousWasteDisposalChart
              data={wasteIndicatorData}
              year={selectedYear}
              fymonth={4}
              loading={false}
            />
            <HazardousWasteDisposalChart
              data={wasteIndicatorData}
              year={selectedYear}
              fymonth={4}
              loading={false}
            />
          </div>
        </section>
      </div> :
        <div>You don't have neccessary permission to access this page </div>
      }
    </div>
  );
};

export default Index;
