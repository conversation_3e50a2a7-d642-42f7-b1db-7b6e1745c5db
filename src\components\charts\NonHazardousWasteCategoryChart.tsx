import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import wasteData from '@/data/wasteData.json';

interface NonHazardousWasteCategoryChartProps {
  selectedMonth?: string;
}

const NonHazardousWasteCategoryChart: React.FC<NonHazardousWasteCategoryChartProps> = ({ selectedMonth = 'all' }) => {
  const { months, categories } = wasteData.nonHazardousCategory;

  // Filter data based on selected month
  const filteredMonths = selectedMonth === 'all' ? months : [selectedMonth];
  const monthIndex = selectedMonth === 'all' ? -1 : months.indexOf(selectedMonth);
  
  const getFilteredData = (data: number[]) => {
    if (selectedMonth === 'all') return data;
    return monthIndex !== -1 ? [data[monthIndex]] : [];
  };

  const series = [
    {
      name: 'Metallic Waste',
      data: getFilteredData(categories.metallicWaste),
      color: 'hsl(var(--chart-non-hazardous))',
      type: 'column' as const
    },
    {
      name: 'Plastic Waste',
      data: getFilteredData(categories.plasticWaste),
      color: 'hsl(220, 70%, 60%)',
      type: 'column' as const
    },
    {
      name: 'Paper Waste',
      data: getFilteredData(categories.paperWaste),
      color: 'hsl(200, 70%, 55%)',
      type: 'column' as const
    },
    {
      name: 'Cardboard',
      data: getFilteredData(categories.cardboard),
      color: 'hsl(180, 70%, 50%)',
      type: 'column' as const
    },
    {
      name: 'Scrapped Plastic/PVC',
      data: getFilteredData(categories.scrappedPlasticPVC),
      color: 'hsl(160, 70%, 45%)',
      type: 'column' as const
    },
    {
      name: 'Rubber Items',
      data: getFilteredData(categories.rubberItems),
      color: 'hsl(140, 70%, 40%)',
      type: 'column' as const
    },
    {
      name: 'Rubbish',
      data: getFilteredData(categories.rubbish),
      color: 'hsl(120, 70%, 35%)',
      type: 'column' as const
    },
    {
      name: 'Thermocoal',
      data: getFilteredData(categories.thermocoal),
      color: 'hsl(100, 70%, 30%)',
      type: 'column' as const
    },
    {
      name: 'Wood',
      data: getFilteredData(categories.wood),
      color: 'hsl(80, 70%, 25%)',
      type: 'column' as const
    },
    {
      name: 'Canteen/Food Waste',
      data: getFilteredData(categories.canteenFoodWaste),
      color: 'hsl(60, 70%, 20%)',
      type: 'column' as const
    }
  ];

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 500,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: filteredMonths,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Waste Generation (Tonnes)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      valueSuffix: ' Tonnes',
      shared: true
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Non-Hazardous Waste Generation By Category</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default NonHazardousWasteCategoryChart;