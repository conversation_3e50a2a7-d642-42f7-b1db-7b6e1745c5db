import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface WasteDataItem {
  indicatorId: string;
  indicatorUnit: string;
  title: string;
  actualTitle: string;
  disposalMethod: string;
  computedValue: string;
  value: string;
  rp: string[];
  reporting_period: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
}

interface WasteGenerationChartProps {
  data?: WasteDataItem[];
  year?: number; // Max year (FY26)
  fymonth?: number; // Fiscal year start month (4 = April)
  loading?: boolean; // Loading state
}

const WasteGenerationChart = ({
  data = [],
  year = 2026,
  fymonth = 4,
  loading = false
}: WasteGenerationChartProps) => {

  // Calculate fiscal years
  const currentFY = year; // FY26
  const previousFY = year - 1; // FY25

  // Generate dynamic chart title
  const dynamicTitle = `Hazardous and Non-Hazardous Waste Generation: FY${previousFY.toString().slice(-2)} vs FY${currentFY.toString().slice(-2)} - Norton`;

  // Helper function to safely convert value to number
  const safeParseValue = (item: WasteDataItem): number => {
    // First try computedValue
    let value = item.computedValue;
    if (value !== null && value !== undefined && value !== '' && value !== '-') {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (!isNaN(numValue)) {
        return numValue;
      }
    }

    // If computedValue is not available, use the value field
    if (item.value !== null && item.value !== undefined) {
      return typeof item.value === 'number' ? item.value : parseFloat(item.value.toString());
    }

    return 0;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Generate fiscal year months based on fymonth parameter
  const generateFiscalYearMonths = (startMonth: number): string[] => {
    const months = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];

    // Reorder based on fiscal year start month
    const startIndex = startMonth - 1; // Convert to 0-based index
    return [...months.slice(startIndex), ...months.slice(0, startIndex)];
  };

  const fiscalMonths = generateFiscalYearMonths(fymonth);

  // Filter data by fiscal year
  const filterDataByFY = (targetFY: number) => {
    return data.filter(item => {
      if (!item.rp || item.rp.length === 0) return false;

      return item.rp.some(monthStr => {
        const fy = getFiscalYear(monthStr, fymonth);
        return fy === targetFY;
      });
    });
  };

  // Get data for each fiscal year
  const fy25Data = filterDataByFY(previousFY);
  const fy26Data = filterDataByFY(currentFY);

  // Process data by month for each fiscal year
  const processDataByMonth = (fyData: WasteDataItem[], targetFY: number) => {
    const hazardousByMonth: number[] = [];
    const nonHazardousByMonth: number[] = [];

    // Generate month keys for the target fiscal year
    const generateMonthKeys = (fy: number, startMonth: number): string[] => {
      const keys = [];
      for (let i = 0; i < 12; i++) {
        const month = ((startMonth - 1 + i) % 12) + 1;
        const year = month >= startMonth ? fy - 1 : fy;
        keys.push(`${month.toString().padStart(2, '0')}-${year}`);
      }
      return keys;
    };

    const monthKeys = generateMonthKeys(targetFY, fymonth);

    monthKeys.forEach(monthKey => {
      let hazardousTotal = 0;
      let nonHazardousTotal = 0;
      let hasHazardousData = false;
      let hasNonHazardousData = false;

      fyData.forEach(item => {
        if (item.rp && item.rp.includes(monthKey)) {
          const value = safeParseValue(item);

          // Check actualTitle to determine if it's Hazardous or Non-Hazardous
          // Based on the screenshot: "Hazardous > Hazardous Solid" vs "Non Hazardous > ..."
          const isHazardous = item.actualTitle && item.actualTitle.toLowerCase().startsWith('hazardous >');
          const isNonHazardous = item.actualTitle && item.actualTitle.toLowerCase().startsWith('non hazardous >');

          if (isHazardous) {
            hazardousTotal += value;
            hasHazardousData = true;
          } else if (isNonHazardous) {
            nonHazardousTotal += value;
            hasNonHazardousData = true;
          }
        }
      });

      // Push null if no data found, or the actual value (including 0 if computedValue was 0)
      hazardousByMonth.push(hasHazardousData ? hazardousTotal : null);
      nonHazardousByMonth.push(hasNonHazardousData ? nonHazardousTotal : null);
    });

    return { hazardous: hazardousByMonth, nonHazardous: nonHazardousByMonth };
  };

  // Handle loading state
  if (loading) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-12 h-12">
                <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
                <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
                <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
              </div>
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle no data state
  if (!data || data.length === 0) {
    return (
      <Card className="shadow-chart">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <p className="text-gray-500 text-lg font-medium">No data to show</p>
              <p className="text-gray-400 text-sm mt-2">No waste generation data available for the selected period</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Process data for both fiscal years
  const fy25ProcessedData = processDataByMonth(fy25Data, previousFY);
  const fy26ProcessedData = processDataByMonth(fy26Data, currentFY);

  const options: Highcharts.Options = {
    chart: {
      type: 'line',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: fiscalMonths,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Waste Generation (Tonnes)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      shared: true,
      formatter: function() {
        let tooltip = `<b>${this.x}</b><br/>`;
        this.points?.forEach(point => {
          tooltip += `<span style="color:${point.color}">${point.series.name}</span>: <b>${point.y?.toFixed(2)} MT</b><br/>`;
        });
        return tooltip;
      }
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: false
        },
        enableMouseTracking: true,
        marker: {
          radius: 4
        }
      }
    },
    series: [
      {
        name: `Hazardous Waste FY'${previousFY.toString().slice(-2)}`,
        data: fy25ProcessedData.hazardous,
        color: 'hsl(var(--chart-hazardous))',
        type: 'line',
        dashStyle: 'Solid'
      },
      {
        name: `Non-Hazardous Waste FY'${previousFY.toString().slice(-2)}`,
        data: fy25ProcessedData.nonHazardous,
        color: 'hsl(var(--chart-non-hazardous))',
        type: 'line',
        dashStyle: 'Solid'
      },
      {
        name: `Hazardous Waste FY'${currentFY.toString().slice(-2)}`,
        data: fy26ProcessedData.hazardous,
        color: 'hsl(var(--chart-hazardous))',
        type: 'line',
        dashStyle: 'Dash'
      },
      {
        name: `Non-Hazardous Waste FY'${currentFY.toString().slice(-2)}`,
        data: fy26ProcessedData.nonHazardous,
        color: 'hsl(var(--chart-non-hazardous))',
        type: 'line',
        dashStyle: 'Dash'
      }
    ],
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{dynamicTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default WasteGenerationChart;