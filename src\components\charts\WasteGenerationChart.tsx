import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import wasteData from '@/data/wasteData.json';

const WasteGenerationChart = () => {
  const { months, hazardous, nonHazardous } = wasteData.wasteGeneration;

  const options: Highcharts.Options = {
    chart: {
      type: 'line',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: '',
    },
    xAxis: {
      categories: months,
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      lineColor: 'hsl(var(--border))',
      tickColor: 'hsl(var(--border))'
    },
    yAxis: {
      title: {
        text: 'Waste Generation (Tonnes)',
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      labels: {
        style: {
          color: 'hsl(var(--muted-foreground))'
        }
      },
      gridLineColor: 'hsl(var(--border))'
    },
    tooltip: {
      valueSuffix: ' Tonnes',
      shared: true
    },
    legend: {
      itemStyle: {
        color: 'hsl(var(--foreground))'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: false
        },
        enableMouseTracking: true,
        marker: {
          radius: 4
        }
      }
    },
    series: [
      {
        name: 'Hazardous Waste FY 25',
        data: hazardous.fy25,
        color: 'hsl(var(--chart-hazardous))',
        type: 'line',
        dashStyle: 'Solid'
      },
      {
        name: 'Hazardous Waste FY 26',
        data: hazardous.fy26,
        color: 'hsl(var(--chart-hazardous))',
        type: 'line',
        dashStyle: 'Dash'
      },
      {
        name: 'Non-Hazardous Waste FY 25',
        data: nonHazardous.fy25,
        color: 'hsl(var(--chart-non-hazardous))',
        type: 'line',
        dashStyle: 'Solid'
      },
      {
        name: 'Non-Hazardous Waste FY 26',
        data: nonHazardous.fy26,
        color: 'hsl(var(--chart-non-hazardous))',
        type: 'line',
        dashStyle: 'Dash'
      }
    ],
    credits: {
      enabled: false
    }
  };

  return (
    <Card className="shadow-chart">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Hazardous and Non-Hazardous Waste Generation: FY 25 vs FY 26 - Norton</CardTitle>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </CardContent>
    </Card>
  );
};

export default WasteGenerationChart;